import inspect

class MyMeta(type):
    """
    这是一个自定义元类。
    当一个类（例如 MyClass）被定义时，这个元类会介入。
    """
    def __new__(mcs, name, bases, attrs):
        # mcs: 元类自身 (MyMeta)
        # name: 类名 (MyClass)
        # bases: 基类元组
        # attrs: 类的属性字典，包含方法、变量等

        # 在这里定义我们希望在其他方法前运行的“前置”方法名
        pre_run_method_name = 'A'

        # 创建一个临时的字典来存放修改后的属性
        new_attrs = {}

        # 遍历原始类的所有属性
        for attr_name, attr_value in attrs.items():
            # 检查是否是需要包装的方法（非特殊方法，也不是 A 本身）
            # 这里我们假设方法名不以下划线开头且不是 A
            if inspect.isfunction(attr_value) and \
               not attr_name.startswith('__') and \
               attr_name != pre_run_method_name:
                
                original_method = attr_value # 原始方法

                def create_wrapped_method(method_to_wrap):
                    """
                    创建一个闭包来捕获原始方法。
                    这个包装器方法将会在执行原始方法前调用 'A'。
                    """
                    def wrapped_method(self, *args, **kwargs):
                        # 确保类实例有 A 方法
                        if hasattr(self, pre_run_method_name) and \
                           callable(getattr(self, pre_run_method_name)):
                            print(f"--- Meta: Automatically calling {pre_run_method_name}() before {method_to_wrap.__name__} ---")
                            getattr(self, pre_run_method_name)() # 自动调用 A()
                        
                        return method_to_wrap(self, *args, **kwargs) # 然后执行原始方法
                    
                    wrapped_method.__name__ = method_to_wrap.__name__ # 保持方法名一致
                    wrapped_method.__doc__ = method_to_wrap.__doc__ # 保持文档字符串一致
                    return wrapped_method
                
                # 用包装器方法替换原始方法
                new_attrs[attr_name] = create_wrapped_method(original_method)
            else:
                # 其他属性（包括 A 方法本身和特殊方法）保持不变
                new_attrs[attr_name] = attr_value
        
        # 使用修改后的属性字典创建新的类
        return super().__new__(mcs, name, bases, new_attrs)

# 使用自定义元类定义一个类
class MyClass(metaclass=MyMeta):
    def __init__(self, value):
        self.value = value
        print(f"MyClass instance '{self.value}' created.")

    def A(self):
        """这是我们希望在其他方法执行前自动运行的方法。"""
        print(f"Executing method A() for '{self.value}' (doing some setup)...")
        self._setup_done = True

    def perform_action_1(self, arg1):
        """一个普通方法，将被元类修改。"""
        if not hasattr(self, '_setup_done') or not self._setup_done:
            print("Warning: perform_action_1 called without A() setup.")
        print(f"Performing action 1 with {arg1} for '{self.value}'.")
        return f"Action 1 result for {arg1}"

    def perform_action_2(self, arg2, kwarg):
        """另一个普通方法，也将被元类修改。"""
        if not hasattr(self, '_setup_done') or not self._setup_done:
            print("Warning: perform_action_2 called without A() setup.")
        print(f"Performing action 2 with {arg2} and {kwarg} for '{self.value}'.")
        return f"Action 2 result for {arg2}, {kwarg}"

    def _private_method(self):
        """私有方法，通常不应被包装（取决于元类逻辑）。"""
        print("Executing private method.")

# --- 运行示例 ---
print("--- Metaclass Example ---")
obj = MyClass("TestObject")
print(obj.perform_action_1("data_a"))
print(obj.perform_action_2("data_b", kwarg="extra_info"))

# 验证 A 方法本身不会被包装
print("\n--- Calling A directly ---")
obj.A()

# 验证私有方法（如果元类逻辑排除了）
print("\n--- Calling private method ---")
obj._private_method()
