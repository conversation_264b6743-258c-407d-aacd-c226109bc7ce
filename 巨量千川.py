import json
import re
import time
import requests
from datetime import date, timedelta, datetime
from tool.Db import get_redis
from 抖音web_api import Api



class Jlqc:
    
    def __init__(self, cookies, csrf_token=None, fp=None, msToken=None) -> None:
        self.cookies = cookies
        self.douyin_web_api = Api()
        if csrf_token is None or fp is None or msToken is None:
            cookies = cookies+';'
            csrf_token = re.search('csrftoken=(.*?);', cookies).group(1)
            # fp = re.search('s_v_web_id=(.*?);', cookies).group(1)
            fp = 'verify_luupradu_lvFX8ARJ_8Fgs_4uRB_B4ZR_mkVp5KVblXCJ'
            msToken = re.search('msToken=(.*?);', cookies)
            if msToken:
                msToken = msToken.group(1)
            else:
                msToken = None
            cookies = cookies[:-1]

        self.csrf_token, self.fp, self.msToken = csrf_token, fp, msToken
        day = date.today()
        self.end_date = day.strftime('%Y-%m-%d 23:59:59')
        day = day-timedelta(days=1)
        self.start_date = day.strftime('%Y-%m-%d 00:00:00')

        session = requests.Session()
        adapter = requests.adapters.HTTPAdapter(max_retries=3)
        session.mount('http://', adapter)
        session.mount('https://', adapter)
        self.session = session
    
    def generate_a_bogus(self,urldata,body,muserAgent):
        data={
            'urldata':urldata,
            'body':body,
            'userAgent':muserAgent
        }
        rsp=requests.post('http://192.168.60.64:8805/',json=data)
        return rsp.text

    def account_marketing_goal_info_全域(self,account:str,start_date:datetime,end_date:datetime):
        """获取全域账户营销目标信息
        路径:竞价推广-全域-推商品下面的数据
        """
        url='https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery'
        params={
            "reqFrom": "overview",
            "gfversion": "1.0.0.7162",
            "aavid": account
        }
        headers = {
            "cookie": self.cookies,
            'x-csrftoken':self.csrf_token
        }
        data= open(r'config\千川\account_marketing_goal_info.json','r',encoding='utf-8').read()
        data=json.loads(data)
        data['StartTime']=start_date.strftime('%Y-%m-%d 00:00:00')
        data['EndTime']=end_date.strftime('%Y-%m-%d 23:59:59')
        data['Filters']['Conditions'][0]['Values']=[account]
        rsp=self.session.post(url,headers=headers,params=params,json=data)
        return rsp.json()
    
    def account_marketing_goal_info_全域_分时(self,account:str,start_date:datetime,end_date:datetime):
        """获取全域账户营销目标信息
        路径:竞价推广-全域-推商品下面的数据
        """
        url='https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery'
        params={
            "reqFrom": "overviewTrend",
            "gfversion": "1.0.1.6678",
            "aavid": account
        }
        headers = {
            "cookie": self.cookies,
            'x-csrftoken':self.csrf_token
        }
        data=json.loads(open('config/千川/account_marketing_goal_info_分时.json','r',encoding='utf-8').read())
        data['StartTime']=start_date.strftime('%Y-%m-%d 00:00:00')
        data['EndTime']=end_date.strftime('%Y-%m-%d 23:59:59')
        data['aavid']=account
        data['Filters']['Conditions'][0]['Values']=[account]
        rsp=self.session.post(url,headers=headers,params=params,json=data)
        return rsp.json()
    
    def account_live_room_info_分时(self,account:str,start_date:datetime,end_date:datetime):
        """获取分时直播间数据
        路径:直播分析-核心数据
        url:https://qianchuan.jinritemai.com/data/live-analysis/detail?roomid=7425754440176536372&aavid=****************
        """
        url='https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery'
        params={
            "reqFrom": "com-trend",
            "gfversion": "1.0.1.6736",
            "aavid": account
        }
        headers = {
            "cookie": self.cookies,
            'x-csrftoken':self.csrf_token
        }
        data=json.loads(open('config/千川/account_live_room_info_分时.json','r',encoding='utf-8').read())
        data['StartTime']=start_date.strftime('%Y-%m-%d 00:00:00')
        data['EndTime']=end_date.strftime('%Y-%m-%d 23:59:59')
        # data['aavid']=account
        # data['Filters']['Conditions'][2]['Values']=[account]
        rsp=self.session.post(url,headers=headers,params=params,json=data)
        return rsp.json()
        
    def account_live_room_info_标准(self,account:str,start_date:datetime,end_date:datetime):
        """获取直播间信息
        路径:数据-数据概览-核心排名-直播间排名

        Args:
            account (str): _description_
        """
        url='https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery?reqFrom=overview_room&gfversion=1.0.1.6678&aavid='+account
        headers = {
            "cookie": self.cookies,
            'x-csrftoken':self.csrf_token
        }
        data= open(r'config\千川\account_live_room_info.json','r',encoding='utf-8').read()
        data=json.loads(data)
        data['StartTime']=start_date.strftime('%Y-%m-%d 00:00:00')
        data['EndTime']=end_date.strftime('%Y-%m-%d 23:59:59')
        data['Filters']['Conditions'][-1]['Values']=[account]
        del data['Filters']['Conditions'][0] # 删除anchor_id
        data['aavid']=account
        rsp=self.session.post(url,json=data,headers=headers)
        return rsp.json()
        
    def sucai_list_全域_推直播(self,account:str,anchor_id:str,start_date:datetime,end_date:datetime,page,page_size=100,filter_id=None):
        """数据-标准推广数据-推直播-素材-视频
        """
        url='https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery'
        params={
            "reqFrom": "roi2_material_list",
            "gfversion": "1.0.1.6672",
            "aavid": account
        }
        data=json.loads(open('config/千川/数据_全域_素材_视频.json','r',encoding='utf-8').read())
        del data['Metrics_str'] # 这个是字段备注
        data['Filters']['Conditions'][2]['Values']=[anchor_id]
        # del data['Filters']['Conditions'][2]
        data['aavid']=account
        data['StartTime']=start_date.strftime('%Y-%m-%d 00:00:00')
        data['EndTime']=end_date.strftime('%Y-%m-%d 23:59:59')
        data['PageParams']['Limit']=page_size
        data['PageParams']['Offset']=(page-1)*page_size
        if filter_id:
            data['Filters']['Conditions'].append({'Field': "roi2_material_name_or_id", 'Values': [filter_id], 'Operator': 7})
        

        hearders = {
            "cookie": self.cookies,
            'x-csrftoken':self.csrf_token
        }
        rsp=self.session.post(url,headers=hearders,params=params,json=data,timeout=5)
        return rsp.json()
    
    def sucai_list_全域_直播间(self,account:str,start_date:datetime,end_date:datetime,page,page_size=100,filter_id=None,stat_time_day=False,marketing_goal=2):
        """获取全域直播间素材数据
        路径: 数据-全域推广-素材分析-视频素材
        
        Args:
            account (str): 广告主ID
            start_date (datetime): 开始时间
            end_date (datetime): 结束时间
            page (int): 页码
            page_size (int): 每页数量
            filter_id (str, optional): 筛选ID. Defaults to None.
            marketing_goal (int) : 2 推直播间 1 推商品
        """
        url='https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery'
        params={
            "reqFrom": "roi2_material_list",
            "gfversion": "1.0.1.3728",
            "aavid": account
        }
        data=json.loads(open('config/千川/数据_全域_直播_素材.json','r',encoding='utf-8').read())
        data['StartTime']=start_date.strftime('%Y-%m-%d 00:00:00')
        data['EndTime']=end_date.strftime('%Y-%m-%d 23:59:59')
        data['Filters']['Conditions'][0]['Values']=[account]
        data['PageParams']['Limit']=page_size
        data['PageParams']['Offset']=(page-1)*page_size
        data['aavid']=account
        # 推商品
        if marketing_goal==1:
            data['Filters']['Conditions'][1]['Values']=['1']
            data['Filters']['Conditions'][2]['Values']=['1']
        if not stat_time_day:
            del data['Dimensions'][data['Dimensions'].index('stat_time_day')]
        if filter_id:
            data['Filters']['Conditions'].append({
                'Field': "material_id",
                'Values': [filter_id],
                'Operator': 7
            })
            
        headers = {
            "cookie": self.cookies,
        }
        rsp=self.session.post(url,headers=headers,params=params,json=data)
        return rsp.json()
    
    def sucai_list_标准(self,account:str,start_date:datetime,end_date:datetime,page,page_size=100,filter_id=None):
        """数据-素材分析-视频素材
        """
        url='https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery'
        params={
            "reqFrom": "material_list",
            "gfversion": "1.0.1.6678",
            "aavid": account
        }
        data=json.loads(open('config/千川/数据_标准_素材_视频.json','r',encoding='utf-8').read())
        del data['Metrics_str'] # 这个是字段备注
        data['Filters']['Conditions'][2]['Values']=[account]
        data['aavid']=account
        data['StartTime']=start_date.strftime('%Y-%m-%d 00:00:00')
        data['EndTime']=end_date.strftime('%Y-%m-%d 23:59:59')
        data['PageParams']['Limit']=page_size
        data['PageParams']['Offset']=(page-1)*page_size
        if filter_id:
            data['Filters']['Conditions'].append({'Field': "material_keyword", 'Values': [filter_id], 'Operator': 7})

        hearders = {
            "cookie": self.cookies,
        }
        rsp=self.session.post(url,headers=hearders,params=params,json=data)
        return rsp.json()
    
    def 全域_推广计划列表_optional(self,account:str,session_id:str):
        """获取全域账户推广计划列表
        """
        url='https://qianchuan.jinritemai.com/ad/api/pmc/v1/uni-promotion/ad/list-optional'
        params={
            'aavid':account,
            'gfversion':'1.0.0.8914'
        }
        data={
            "_origin_ajax_": 1,
            "SessionID": session_id,
            "ListAdsModules": [
                28,
                29,
                34,
                9,
                7,
                8,
                16,
                14,
                17,
                25,
                26,
                21,
                36,
                40,
                37
            ],
            "_fetch_start_": int(time.time()*1000), #************* 
            "aavid": account
            }
        headers = {
            'cookie': self.cookies,
            'content-type': 'application/json',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36',
        }
        params['a_bogus'] = self.generate_a_bogus(json.dumps(params),json.dumps(data),headers['user-agent'])
        rsp=self.session.post(url,headers=headers,params=params,json=data)
        return rsp.json()
    
    def 全域_推广计划列表(self, account:str, start_date:datetime, end_date:datetime, search_keyword=None, page=1, page_size=10):
        """获取全域账户推广计划列表
        ps: 目前只获 取了推商品的参数,应该会需要dataSetKey,mar_goal
        Args:
            account (str): 账户id
            start_date (datetime): 开始时间
            end_date (datetime): 结束时间
            search_keyword (str, optional): 搜索关键词. Defaults to None.
            page (int, optional): 页码. Defaults to 1.
            page_size (int, optional): 每页数量. Defaults to 10.
        """
        url = 'https://qianchuan.jinritemai.com/ad/api/pmc/v1/uni-promotion/ad/list-required'
        params = {
            'aavid': account,
            'gfversion': '1.0.0.8619'
        }
        
        data = {
            "mar_goal": 1,
            "dataSetKey": "product_roi2_promotion",
            "page": page,
            "page_size": page_size,
            "order_by_type": 2,
            "order_by_field": "create_time",
            "start_time": start_date.strftime('%Y-%m-%d %H:%M:%S'),
            "end_time": end_date.strftime('%Y-%m-%d %H:%M:%S'),
            "smartBidType": 0,
            "ad_cost_status": -1,
            "ad_status_filter_type": 0,
            "metrics": [
                "stat_cost_for_roi2",
                "stat_cost_for_roi2_primary",
                "total_pay_order_count_for_roi2", 
                "total_pay_order_count_for_roi2_primary",
                "total_pay_order_gmv_for_roi2",
                "total_pay_order_gmv_for_roi2_primary",
                "total_prepay_and_pay_order_roi2",
                "total_prepay_and_pay_order_roi2_primary",
                "total_cost_per_pay_order_for_roi2",
                "total_cost_per_pay_order_for_roi2_primary",
                "total_pay_order_coupon_amount_for_roi2",
                "total_pay_order_coupon_amount_for_roi2_primary"
            ],
            "listModules": [10, 4, 20],
            "needRequestOptional": True,
            "aavid": account
        }

        if search_keyword:
            data["search_keyword"] = search_keyword
            data["search_type"] = 1

        headers = {
            'cookie': self.cookies,
            'content-type': 'application/json'
        }

        response = self.session.post(url, headers=headers, params=params, json=data)
        return response.json()
    
    def 全域_get_account_anchor_id(self, account:str, start_date:datetime, end_date:datetime):
        """获取全域账户主播id
        说明:用于获取anchor_id 提供给[全域_创意_直播画面] 使用 ,返回其他参数作用没研究
        adInfos_id 用于获取素材信息
        """
        url='https://qianchuan.jinritemai.com/ad/api/pmc/v1/uni-promotion/ad/list-required'
        params={
            'aavid':account,
            'gfversion':'1.0.0.6691'
        }
        
        data={
            "mar_goal": 2,
            "page_size": 10,
            "order_by_type": 2,
            "order_by_field": "create_time",
            "page_type": "600411",
            "metrics": [
                "stat_cost",
                "total_pay_order_gmv_for_roi2",
                "total_prepay_and_pay_order_roi2"
            ],
            "dataSetKey": "site_promotion_list",
            "page": 1,
            "start_time": f"{start_date.strftime('%Y-%m-%d')} 00:00:00",
            "end_time": f"{end_date.strftime('%Y-%m-%d')} 23:59:59",
            "smartBidType": 0,
            "listModules": [
                10,
                7
            ],
            "aavid": account
        }
        hearders = {
            "cookie": self.cookies,
        }
        rsp=self.session.post(url,headers=hearders,params=params,json=data)
        if rsp.status_code!=200:
            print(rsp.text)
            return None,None
        rsp_json=json.loads(rsp.text)
        # 历史的
        # adInfos_id=rsp_json['data']['adInfos'][0]['id']
        # data=rsp_json['data']['adRoomInfoMap']
        # anchor_id=data[list(data.keys())[0]][0]['anchor']['id']
        
        rsp_json= rsp_json['data']['adRoomInfoMap']
        if len(rsp_json.keys())==1:
            adInfos_id=list(rsp_json.keys())[0]
            anchor_id=rsp_json[adInfos_id][0]['anchor']['id']
        elif not rsp_json:
            # 没有数据 rsp_json={}
            return None,None
        else:
            n_rsp_json= [[k,rsp_json[k][0]['anchor']['id']] for k in rsp_json if rsp_json[k][0]['roomStatus']==2]
            if not n_rsp_json:
                adInfos_id=list(rsp_json.keys())[0]
                anchor_id=rsp_json[adInfos_id][0]['anchor']['id']
            else:
                adInfos_id,anchor_id=n_rsp_json[0]
        return anchor_id,adInfos_id
    
    def 全域_获取素材信息(self, account:str,anchor_id:str,adInfos_id:str,素材ids:list,start_date:datetime, end_date:datetime):
        """获取全域素材信息
        """
        url='https://qianchuan.jinritemai.com/ad/api/pmc/v1/uni-promotion/material/list-optional'
        params={
            'aavid':account,
            'gfversion':'1.0.0.7108'
        }
        data={
            "ObjectType": 1,
            "ObjectID": anchor_id,
            "LegoMidList": 素材ids,
            "OptionalModules": [
                "material_heat_module",
                "audit_info_module",
                "video_suggest_module",
                "block_info_module"
            ],
            "MaterialType": 3,
            "MarGoal": 2, # 视频 2
            "AggregateAid": adInfos_id,
            "StartTime": f"{start_date.strftime('%Y-%m-%d')} 00:00:00",
            "EndTime": f"{end_date.strftime('%Y-%m-%d')} 23:59:59",
            "aavid": account
        }
        hearders = {
            "cookie": self.cookies,
        }
        rsp=self.session.post(url,headers=hearders,params=params,json=data)
        return rsp.json()
    
    def 全域_创意_视频_同质化(self, account:str,anchor_id:str, start_date:datetime, end_date:datetime,page=1,page_size=100,key_type='10'):
        """获取全域创意视频同质/低质化数据
        
        params:
            key_type:   10 低质 7 同质 5 低效
            
        路径:https://qianchuan.jinritemai.com/promotion-v2/uni/detail?aavid=****************&awemeId=&latestAweme=&videoId=&adId=****************&adIds=&mg=1&cs=-1&ct=1&dr=2024-11-04%2C2024-11-04&isShowSearchGuideModal=&sourceFrom=&utm_source=&utm_medium=&utm_campaign=&utm_content=&utm_term=&uniDetail=%7B%7D#adr=%7B%7D&diaNum=0&needDia=0&umg=2&usbt=0&dut=2024-11-04%2016%3A19&uniDetail=%7B%22tb%22%3A%22creative%22%2C%22edc%22%3A%22liveRace%22%2C%22cst%22%3A%220%22%2C%22bcf%22%3A%22%7B%5C%22fs%5C%22%3A%5C%22%257B%2522mar_goal%2522%253A2%252C%2522metrics%2522%253A%255B%2522stat_cost%2522%252C%2522total_prepay_and_pay_order_roi2%2522%252C%2522total_pay_order_gmv_for_roi2%2522%252C%2522total_pay_order_count_for_roi2%2522%252C%2522total_cost_per_pay_order_for_roi2%2522%252C%2522total_prepay_order_count_for_roi2%2522%252C%2522total_prepay_order_gmv_for_roi2%2522%252C%2522interfere_tool%2522%252C%2522live_plan%2522%255D%252C%2522dataSetKey%2522%253A%2522site_promotion_list%2522%252C%2522page%2522%253A1%252C%2522page_size%2522%253A10%252C%2522order_by_type%2522%253A2%252C%2522order_by_field%2522%253A%2522create_time%2522%252C%2522start_time%2522%253A%25222024-11-04%252000%253A00%253A00%2522%252C%2522end_time%2522%253A%25222024-11-04%252023%253A59%253A59%2522%252C%2522smartBidType%2522%253A0%257D%5C%22%2C%5C%22cfs%5C%22%3A%5C%22%7B%7D%5C%22%2C%5C%22bfs%5C%22%3A%5C%22%7B%7D%5C%22%2C%5C%22afs%5C%22%3A%5C%22%7B%7D%5C%22%2C%5C%22pfs%5C%22%3A%5C%22%7B%7D%5C%22%2C%5C%22aId%5C%22%3A%5C%22%5C%22%2C%5C%22jf%5C%22%3A%5C%22uni%5C%22%2C%5C%22uAId%5C%22%3A%5C%22****************%5C%22%2C%5C%22cId%5C%22%3A%5C%22%5C%22%2C%5C%22bpId%5C%22%3A%5C%22%5C%22%2C%5C%22agId%5C%22%3A%5C%22%5C%22%2C%5C%22awemeId%5C%22%3A%5C%22%5C%22%2C%5C%22pdId%5C%22%3A%5C%22%5C%22%7D%22%2C%22cc%22%3A%22%7B%5C%22sk%5C%22%3A%5C%22%5C%22%2C%5C%22ccft%5C%22%3A%5C%220%5C%22%2C%5C%22p%5C%22%3A%5C%221%5C%22%2C%5C%22ps%5C%22%3A%5C%2210%5C%22%2C%5C%22st%5C%22%3A%5C%22asc%5C%22%2C%5C%22sf%5C%22%3A%5C%22%5C%22%7D%22%2C%22ad%22%3A%22%7B%5C%22p%5C%22%3A%5C%221%5C%22%2C%5C%22ps%5C%22%3A%5C%22100%5C%22%2C%5C%22adsk%5C%22%3A%5C%22%5C%22%2C%5C%22aud%5C%22%3A%5C%22%7B%7D%5C%22%2C%5C%22sf%5C%22%3A%5C%22%5C%22%2C%5C%22st%5C%22%3A%5C%22desc%5C%22%2C%5C%22asft%5C%22%3A%5C%220%5C%22%2C%5C%22amf%5C%22%3A%5C%220%5C%22%2C%5C%22bt%5C%22%3A%5C%22-1%5C%22%2C%5C%22ea%5C%22%3A%5C%22%5C%22%2C%5C%22op%5C%22%3A%5C%22-1%5C%22%2C%5C%22cos%5C%22%3A%5C%22-1%5C%22%2C%5C%22act%5C%22%3A%5C%22%2C%5C%22%7D%22%2C%22cri%22%3A%22%7B%5C%22sk%5C%22%3A%5C%22%5C%22%2C%5C%22inAds%5C%22%3A%5C%22%5C%22%2C%5C%22csft%5C%22%3A%5C%220%5C%22%2C%5C%22st%5C%22%3A%5C%22asc%5C%22%2C%5C%22sf%5C%22%3A%5C%22%5C%22%2C%5C%22p%5C%22%3A%5C%221%5C%22%2C%5C%22ps%5C%22%3A%5C%2210%5C%22%2C%5C%22ct%5C%22%3A%5C%220%5C%22%2C%5C%22im%5C%22%3A%5C%22%5C%22%7D%22%2C%22mtrc%22%3A%22%7B%5C%22p%5C%22%3A%5C%221%5C%22%2C%5C%22ps%5C%22%3A%5C%2210%5C%22%2C%5C%22tb%5C%22%3A%5C%22%5C%22%2C%5C%22kw%5C%22%3A%5C%22%5C%22%2C%5C%22sd%5C%22%3A%5C%22%7B%5C%5C%5C%22option%5C%5C%5C%22%3A1%7D%5C%22%2C%5C%22sf%5C%22%3A%5C%22stat_cost%5C%22%2C%5C%22st%5C%22%3A%5C%22desc%5C%22%2C%5C%22sc%5C%22%3A%5C%22%5C%22%2C%5C%22sug%5C%22%3A%5C%22%5C%22%2C%5C%22md%5C%22%3A%5C%22%5C%22%2C%5C%22hct%5C%22%3A%5C%221%5C%22%7D%22%2C%22source%22%3A%22%7B%5C%22sourceName%5C%22%3A%5C%22%5C%22%2C%5C%22sourceId%5C%22%3A%5C%22%5C%22%2C%5C%22agId%5C%22%3A%5C%22%5C%22%7D%22%7D
        """
        url='https://qianchuan.jinritemai.com/ad/api/pmc/v1/uni-promotion/material/list-required'
        parmas={"aavid":account,"gfversion":"1.0.0.7406"}
        data=json.loads(open(r'config\千川\全域_创意_视频_低质.json','r',encoding='utf-8').read())
        data['Filters']['Conditions'][4]['Values']=[anchor_id]
        data['PageParams']['Limit']=page_size
        data['PageParams']['Offset']=(page-1)*page_size
        data['StartTime']=start_date.strftime('%Y-%m-%d 00:00:00')
        data['EndTime']=end_date.strftime('%Y-%m-%d 23:59:59')
        data['aavid']=account
        # 10 低质 7 同质 5 低效
        data['Filters']['Conditions'][3]['Values']=[key_type]
        
        hearders = {
            "cookie": self.cookies,
        }
        rsp=self.session.post(url,headers=hearders,params=parmas,json=data)
        return rsp.json()
        
    def 全域_创意2(self, account:str,anchor_id:str, start_date:datetime, end_date:datetime,quert_type:str='视频',page=1,page_size=100):
        """获取全域创意直播画面数据
        接口路径:
            https://qianchuan.jinritemai.com/promotion-v2/uni/detail?aavid=****************&awemeId=&latestAweme=&videoId=&adId=****************&adIds=&mg=2&cs=-1&ct=1&dr=2024-09-09%2C2024-09-09&isShowSearchGuideModal=&sourceFrom=&utm_source=&utm_medium=&utm_campaign=&utm_content=&utm_term=&uniDetail=%7B%7D#adr=%7B%7D&diaNum=0&needDia=0&umg=2&usbt=0&dut=2024-09-09%2016%3A45&uniDetail=%7B%22tb%22%3A%22creative%22%2C%22edc%22%3A%22liveRace%22%2C%22cst%22%3A%220%22%2C%22bcf%22%3A%22%7B%5C%22fs%5C%22%3A%5C%22%257B%2522mar_goal%2522%253A2%252C%2522metrics%2522%253A%255B%2522stat_cost%2522%252C%2522total_prepay_and_pay_order_roi2%2522%252C%2522total_pay_order_gmv_for_roi2%2522%252C%2522total_pay_order_count_for_roi2%2522%252C%2522total_cost_per_pay_order_for_roi2%2522%252C%2522total_prepay_order_count_for_roi2%2522%252C%2522total_prepay_order_gmv_for_roi2%2522%252C%2522interfere_tool%2522%252C%2522live_plan%2522%255D%252C%2522dataSetKey%2522%253A%2522site_promotion_list%2522%252C%2522page%2522%253A1%252C%2522page_size%2522%253A10%252C%2522order_by_type%2522%253A2%252C%2522order_by_field%2522%253A%2522create_time%2522%252C%2522start_time%2522%253A%25222024-09-09%252000%253A00%253A00%2522%252C%2522end_time%2522%253A%25222024-09-09%252023%253A59%253A59%2522%252C%2522smartBidType%2522%253A0%257D%5C%22%2C%5C%22cfs%5C%22%3A%5C%22%7B%7D%5C%22%2C%5C%22bfs%5C%22%3A%5C%22%7B%7D%5C%22%2C%5C%22afs%5C%22%3A%5C%22%7B%7D%5C%22%2C%5C%22pfs%5C%22%3A%5C%22%7B%7D%5C%22%2C%5C%22aId%5C%22%3A%5C%22%5C%22%2C%5C%22jf%5C%22%3A%5C%22uni%5C%22%2C%5C%22uAId%5C%22%3A%5C%22****************%5C%22%2C%5C%22cId%5C%22%3A%5C%22%5C%22%2C%5C%22bpId%5C%22%3A%5C%22%5C%22%2C%5C%22agId%5C%22%3A%5C%22%5C%22%2C%5C%22awemeId%5C%22%3A%5C%22%5C%22%2C%5C%22pdId%5C%22%3A%5C%22%5C%22%7D%22%2C%22cc%22%3A%22%7B%5C%22sk%5C%22%3A%5C%22%5C%22%2C%5C%22ccft%5C%22%3A%5C%220%5C%22%2C%5C%22p%5C%22%3A%5C%221%5C%22%2C%5C%22ps%5C%22%3A%5C%2210%5C%22%2C%5C%22st%5C%22%3A%5C%22asc%5C%22%2C%5C%22sf%5C%22%3A%5C%22%5C%22%7D%22%2C%22ad%22%3A%22%7B%5C%22p%5C%22%3A%5C%221%5C%22%2C%5C%22ps%5C%22%3A%5C%2210%5C%22%2C%5C%22adsk%5C%22%3A%5C%22%5C%22%2C%5C%22aud%5C%22%3A%5C%22%7B%7D%5C%22%2C%5C%22sf%5C%22%3A%5C%22%5C%22%2C%5C%22st%5C%22%3A%5C%22desc%5C%22%2C%5C%22asft%5C%22%3A%5C%220%5C%22%2C%5C%22amf%5C%22%3A%5C%220%5C%22%2C%5C%22bt%5C%22%3A%5C%22-1%5C%22%2C%5C%22ea%5C%22%3A%5C%22%5C%22%2C%5C%22op%5C%22%3A%5C%22-1%5C%22%2C%5C%22cos%5C%22%3A%5C%22-1%5C%22%2C%5C%22act%5C%22%3A%5C%22%2C%5C%22%7D%22%2C%22cri%22%3A%22%7B%5C%22sk%5C%22%3A%5C%22%5C%22%2C%5C%22inAds%5C%22%3A%5C%22%5C%22%2C%5C%22csft%5C%22%3A%5C%220%5C%22%2C%5C%22st%5C%22%3A%5C%22asc%5C%22%2C%5C%22sf%5C%22%3A%5C%22%5C%22%2C%5C%22p%5C%22%3A%5C%221%5C%22%2C%5C%22ps%5C%22%3A%5C%2210%5C%22%2C%5C%22ct%5C%22%3A%5C%220%5C%22%2C%5C%22im%5C%22%3A%5C%22%5C%22%7D%22%2C%22mtrc%22%3A%22%7B%5C%22p%5C%22%3A%5C%221%5C%22%2C%5C%22ps%5C%22%3A%5C%2210%5C%22%2C%5C%22tb%5C%22%3A%5C%22%5C%22%2C%5C%22kw%5C%22%3A%5C%22%5C%22%2C%5C%22sd%5C%22%3A%5C%22%7B%5C%5C%5C%22option%5C%5C%5C%22%3A1%7D%5C%22%2C%5C%22sf%5C%22%3A%5C%22stat_cost%5C%22%2C%5C%22st%5C%22%3A%5C%22desc%5C%22%2C%5C%22sc%5C%22%3A%5C%22%5C%22%2C%5C%22sug%5C%22%3A%5C%22%5C%22%2C%5C%22md%5C%22%3A%5C%22%5C%22%2C%5C%22hct%5C%22%3A%5C%221%5C%22%7D%22%2C%22source%22%3A%22%7B%5C%22sourceName%5C%22%3A%5C%22%5C%22%2C%5C%22sourceId%5C%22%3A%5C%22%5C%22%2C%5C%22agId%5C%22%3A%5C%22%5C%22%7D%22%7D
            竞价推广_全域_抖音号点击
        """
        url='https://qianchuan.jinritemai.com/ad/api/pmc/v1/uni-promotion/material/list-required'
        params={
            'aavid':account,
            'gfversion':'1.0.0.6982'
        }
        if quert_type=='视频':
            data=json.loads(open('config/千川/竞价推广_全域_抖音号点击_视频.json','r',encoding='utf-8').read())
            data['Filters']['Conditions'][4]['Values']=[anchor_id]
            data['PageParams']['Limit']=page_size
            data['PageParams']['Offset']=(page-1)*page_size
        elif quert_type=='直播':
            data=json.loads(open('config/千川/竞价推广_全域_抖音号点击_直播.json','r',encoding='utf-8').read())
            data['Filters']['Conditions'][3]['Values']=[anchor_id]
            data['PageParams']['Limit']=page_size
            data['PageParams']['Offset']=(page-1)*page_size
        data['StartTime']=start_date.strftime('%Y-%m-%d 00:00:00')
        data['EndTime']=end_date.strftime('%Y-%m-%d 23:59:59')
        data['aavid']=account
        hearders = {
            "cookie": self.cookies,
        }
        rsp=self.session.post(url,headers=hearders,params=params,json=data)
        return rsp.json()
    
    def 全域_创意(self, account:str,anchor_id:str, start_date:datetime, end_date:datetime,quert_type:str='直播'):
        """获取全域创意直播画面数据
        接口路径:
            https://qianchuan.jinritemai.com/data/overview?aavid=****************&utm_campaign=&utm_content=&utm_medium=&utm_source=&utm_term=
            今日实时数据
        """
        url='https://qianchuan.jinritemai.com/ad/api/pmc/v1/uni-promotion/material/list-required'
        params={
            'aavid':'****************',
            'gfversion':'1.0.0.6691'
        }
        if quert_type=='直播':
            data={
            "Metrics": [
                "live_show_count_exclude_video_for_roi2",
                "live_watch_count_exclude_video_for_roi2",
                "live_cvr_rate_exclude_video_for_roi2",
                "live_convert_rate_exclude_video_for_roi2",
                "total_pay_order_count_for_roi2_fork",
                "total_pay_order_gmv_for_roi2_fork",
                "stat_cost_for_roi2_fork",
                "cost_rate_for_roi2_fork",
                "basic_stat_cost_for_roi2_v2_fork",
                "total_prepay_order_count_for_roi2_fork",
                "total_cost_per_pay_order_for_roi2_fork",
                "total_prepay_order_gmv_for_roi2_fork",
                "total_pay_order_gmv_rate_for_roi2_fork",
                "total_unfinished_estimate_order_gmv_for_roi2_fork",
                "total_pay_order_coupon_amount_for_roi2_fork",
                "live_follow_count_for_roi2",
                "live_comment_count_for_roi2",
                "live_like_count_for_roi2"
            ],
            "Filters": {
                "ConditionRelationshipType": 1,
                "Conditions": [
                    {
                        "Field": "roi2_material_type_v3",
                        "Operator": 7,
                        "Values": [
                            "4"
                        ]
                    },
                    {
                        "Field": "marketing_goal",
                        "Operator": 7,
                        "Values": [
                            "2"
                        ]
                    },
                    {
                        "Field": "aggregate_smart_bid_type",
                        "Operator": 7,
                        "Values": [
                            "0"
                        ]
                    },
                    {
                        "Field": "anchor_id",
                        "Operator": 7,
                        "Values": [
                           anchor_id
                        ]
                    }
                ]
            },
            "StartTime": start_date.strftime('%Y-%m-%d 00:00:00'),
            "EndTime": end_date.strftime('%Y-%m-%d 23:59:59'),
            "PageParams": {
                "Limit": 10,
                "Offset": 0
            },
            "DataSetKey": "site_promotion_post_data_live",
            "Dimensions": [
                "anchor_id",
                "roi2_304_cid",
                "roi2_material_anchor_name",
                "roi2_material_anchor_show_id",
                "roi2_material_anchor_icon"
            ],
            "aavid": account
        }
        else:
            data={
                "Metrics": [
                    "live_show_count_for_roi2_v2",
                    "live_watch_count_for_roi2_v2",
                    "live_cvr_rate_for_roi2_v2",
                    "live_convert_rate_for_roi2_v2",
                    "total_pay_order_count_for_roi2",
                    "total_pay_order_gmv_for_roi2",
                    "stat_cost_for_roi2",
                    "cost_rate_for_roi2",
                    "basic_stat_cost_for_roi2_v2",
                    "total_cost_per_pay_order_for_roi2",
                    "total_prepay_and_pay_order_roi2",
                    "total_pay_order_gmv_rate_for_roi2",
                    "total_prepay_order_gmv_for_roi2",
                    "total_prepay_order_count_for_roi2",
                    "total_unfinished_estimate_order_gmv_for_roi2",
                    "total_pay_order_coupon_amount_for_roi2"
                ],
                "Filters": {
                    "ConditionRelationshipType": 1,
                    "Conditions": [
                        {
                            "Field": "roi2_material_type_v3",
                            "Operator": 7,
                            "Values": [
                                "3"
                            ]
                        },
                        {
                            "Field": "marketing_goal",
                            "Operator": 7,
                            "Values": [
                                "2"
                            ]
                        },
                        {
                            "Field": "aggregate_smart_bid_type",
                            "Operator": 7,
                            "Values": [
                                "0"
                            ]
                        },
                        {
                            "Field": "roi2_material_status",
                            "Operator": 7,
                            "Values": [
                                "1"
                            ]
                        },
                        {
                            "Field": "anchor_id",
                            "Operator": 7,
                            "Values": [
                                anchor_id
                            ]
                        }
                    ]
                },
                "StartTime": start_date.strftime('%Y-%m-%d 00:00:00'),
                "EndTime": end_date.strftime('%Y-%m-%d 23:59:59'),
                "PageParams": {
                    "Limit": 10,
                    "Offset": 0
                },
                "OrderBy": [
                    {
                        "Type": 2,
                        "Field": "live_show_count_for_roi2_v2"
                    }
                ],
                "DataSetKey": "site_promotion_post_data_video",
                "Dimensions": [
                    "material_id",
                    "roi2_material_status",
                    "roi2_material_video_type",
                    "roi2_material_video_name",
                    "roi2_material_video_play_info",
                    "roi2_material_upload_time"
                ],
                "aavid": account
            }
        hearders = {
            "cookie": self.cookies,
        }
        rsp=self.session.post(url,headers=hearders,params=params,json=data)
        return rsp.json()
    
    def get_ad_hourly_data(self, account:str, start_date:datetime, end_date:datetime):
        """获取广告计划小时级数据
        路径: 竞价推广-计划详情-趋势图数据
        url = https://qianchuan.jinritemai.com/uni-prom/detail?aavid=****************&awemeId=&latestAweme=&videoId=&adId=****************&ct=1&dr=2025-05-29%2C2025-05-29&sourceFrom=&utm_source=&utm_medium=&utm_campaign=&utm_content=&utm_term=&liveAwemeId=&liveQcpxMode=0&liveAdId=&ncrAdId=&ncrStartTime=&ncrEndTime=&ncrIsFirstReport=&ncrZhanneixin=&autoShowGrabFirstScreen=&uniBatchTaskId=&uniDetail=%7B%7D#adr=%7B%7D&umg=2&usbt=0&dut=2025-05-29%2014%3A05&uniDetail=%7B%22tb%22%3A%22data%22%2C%22edc%22%3A%22liveRace%22%2C%22cst%22%3A%220%22%2C%22bcf%22%3A%22%7B%5C%22fs%5C%22%3A%5C%22%257B%2522mar_goal%2522%253A2%252C%2522metrics%2522%253A%255B%2522stat_cost%2522%252C%2522total_prepay_and_pay_order_roi2%2522%252C%2522total_pay_order_gmv_for_roi2%2522%252C%2522total_pay_order_count_for_roi2%2522%252C%2522total_cost_per_pay_order_for_roi2%2522%252C%2522total_prepay_order_count_for_roi2%2522%252C%2522total_prepay_order_gmv_for_roi2%2522%255D%252C%2522dataSetKey%2522%253A%2522site_promotion_list%2522%252C%2522page%2522%253A1%252C%2522page_size%2522%253A10%252C%2522order_by_type%2522%253A2%252C%2522order_by_field%2522%253A%2522create_time%2522%252C%2522start_time%2522%253A%25222025-05-29%252000%253A00%253A00%2522%252C%2522end_time%2522%253A%25222025-05-29%252023%253A59%253A59%2522%252C%2522smartBidType%2522%253A0%257D%5C%22%2C%5C%22cfs%5C%22%3A%5C%22%7B%7D%5C%22%2C%5C%22bfs%5C%22%3A%5C%22%7B%7D%5C%22%2C%5C%22afs%5C%22%3A%5C%22%7B%7D%5C%22%2C%5C%22pfs%5C%22%3A%5C%22%7B%7D%5C%22%2C%5C%22aId%5C%22%3A%5C%22%5C%22%2C%5C%22jf%5C%22%3A%5C%22uniAd%5C%22%2C%5C%22uAId%5C%22%3A%5C%22****************%5C%22%2C%5C%22cId%5C%22%3A%5C%22%5C%22%2C%5C%22bpId%5C%22%3A%5C%22%5C%22%2C%5C%22agId%5C%22%3A%5C%22%5C%22%2C%5C%22awemeId%5C%22%3A%5C%22%5C%22%2C%5C%22pdId%5C%22%3A%5C%22%5C%22%7D%22%2C%22cc%22%3A%22%7B%5C%22sk%5C%22%3A%5C%22%5C%22%2C%5C%22ccft%5C%22%3A%5C%220%5C%22%2C%5C%22p%5C%22%3A%5C%221%5C%22%2C%5C%22ps%5C%22%3A%5C%2210%5C%22%2C%5C%22st%5C%22%3A%5C%22asc%5C%22%2C%5C%22sf%5C%22%3A%5C%22%5C%22%7D%22%7D
        Args:
            account (str): 账户id
            start_date (datetime): 开始时间
            end_date (datetime): 结束时间
            
        Returns:
            dict: 返回广告计划小时级数据
        """
        url = "https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery"
        params = {
            "reqFrom": "compareTrend",
            "aavid": account,
            "gfversion": "1.0.0.1437"
        }
        
        data = {
            "Metrics": [
                "stat_cost",
                "total_prepay_and_pay_order_roi2",
                "total_pay_order_gmv_for_roi2",
                "total_pay_order_count_for_roi2",
                "total_cost_per_pay_order_for_roi2",
                "total_prepay_order_count_for_roi2",
                "total_prepay_order_gmv_for_roi2",
                "total_unfinished_estimate_order_gmv_for_roi2",
                "total_pay_order_coupon_amount_for_roi2"
            ],
            "DataSetKey": "site_promotion_ad_table_list",
            "OrderBy": [
                {
                    "Type": 1,
                    "Field": "stat_time_hour"
                }
            ],
            "Dimensions": [
                "stat_time_hour"
            ],
            "Filters": {
                "ConditionRelationshipType": 1,
                "Conditions": [
                    {
                        "Field": "advertiser_id",
                        "Operator": 7,
                        "Values": [account]
                    }
                ]
            },
            "StartTime": start_date.strftime("%Y-%m-%d %H:%M:%S"),
            "EndTime": end_date.strftime("%Y-%m-%d %H:%M:%S"),
            "aavid": account
        }
        
        headers = {
            'cookie': self.cookies,
            'content-type': 'application/json',
            'x-csrftoken': self.csrf_token,
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        
        response = self.session.post(url, params=params, headers=headers, json=data)
        return response.json()
    
    def get_account_data(self, account:str, start_date:datetime, end_date:datetime):
        """获取账户推广数据
        接口路径:
            https://qianchuan.jinritemai.com/data/overview?aavid=****************&utm_campaign=&utm_content=&utm_medium=&utm_source=&utm_term=
            今日实时数据
        说明:
            该接口可以指定时间日期,可回溯.
            如果获取6-7 total会合计6点和7点,他是按小时来统计的.

        Args:
            account (_type_): _description_
            start_date (_type_, optional): _description_. Defaults to None.
            end_date (_type_, optional): _description_. Defaults to None.
        """
        url = 'https://qianchuan.jinritemai.com/ad/api/data/v1/overview/data?'+f'gfversion=1.0.1.6405&aavid='+account
        start_date = start_date.strftime('%Y-%m-%d %H:%M:%S')
        end_date = end_date.strftime('%Y-%m-%d %H:%M:%S')
        data = {
            "DataSetKey": "data_overview",
            "Metrics": [
                "stat_cost",
                "show_cnt",
                "ctr",
                "pay_order_count",
                "pay_order_amount",
                "prepay_and_pay_order_roi"
            ],
            "Dimensions": [
                "stat_time_hour"
            ],
            "StartTime": start_date,#"2024-08-01 00:00:00",
            "EndTime": end_date,#"2024-08-01 16:23:26",
            # "ComparisonParams": {
            #     "RatioStartTime": "2024-07-31 00:00:00",
            #     "RatioEndTime": "2024-07-31 16:23:26"
            # },
            "Filters": {
                "Conditions": [
                    {
                        "Field": "advertiser_id",
                        "Values": [
                            account
                        ],
                        "Operator": 7
                    },
                    {
                        "Field": "pricing_category",
                        "Values": [
                            "2"
                        ],
                        "Operator": 7
                    },
                    {
                        "Field": "ecp_app_id",
                        "Values": [
                            "1",
                            "2"
                        ],
                        "Operator": 7
                    }
                ],
                "ConditionRelationshipType": 1
            },
            "aavid": account
        }
        hearders = {
            "cookie": self.cookies,
        }
        
        rsp= self.session.post(url=url,headers=hearders, json=data)
        # print(rsp.text)
        return rsp.json()
        
        
    def getListRooms(self, anchor_ids, aavid, date_time, base_data=False):
        """获取直播间数据
            url: https://qianchuan.jinritemai.com/data-report/live-analysis?curtab=today-live&filters=%7B%22roomStatus%22%3A10,%22roomDeliveryStatus%22%3A10%7D&anchor_ids=****************&date=2024-07-16&ad_origin=-1&aavid=****************
        Args:
            anchor_ids (_type_): _description_
            aavid (_type_): _description_
            date_time (_type_): _description_
        """
        url = 'https://qianchuan.jinritemai.com/ad/ecom/marketing/api/v1/todayLive/getListRooms'
        params = {
            "date_time": date_time,  # "2024-07-15",
            "room_status": "10",
            "delivery_status": "10",
            "metrics": "stat_cost,luban_live_pay_order_gpm,cpc_platform,ecp_convert_rate,ecp_cpa_platform,live_pay_order_gmv_alias,luban_live_pay_order_gmv,live_pay_order_gmv_roi,ad_live_prepay_and_pay_order_gmv_roi,live_create_order_count_alias,luban_live_order_count,live_pay_order_count_alias,luban_live_pay_order_count,live_pay_order_gmv_avg,luban_live_prepay_order_count,live_prepay_order_count_alias,live_order_refund_count_7d,stat_cost,ad_live_prepay_and_pay_order_gmv_roi,live_pay_order_gmv_alias",
            "order_by_type": "2",
            "page": "1",
            "page_size": "20",
            "anchor_ids": anchor_ids,
            "stats_authority": "-1",
            "page_type": "16001",
            "aavid": aavid,
            "gfversion": "1.0.1.6345"
        }
        hearders = {
            "cookie": self.cookies,
        }
        rsp = self.session.get(url, headers=hearders, params=params)
        if base_data:
            return rsp.json()
        else:
            rows = rsp.json()
            rows = rows['data']['roomStatsMap']
            rows = rows[list(rows.keys())[0]]['metrics']
            rows = {rows[k]['metricsZh']: rows[k]['value'] for k in rows}
            return rows

    def standard_get_summary_info_search(self, account, search_key=None, start_time=None, end_time=None, mar_goal=1):
        """获取账户汇总信息

        Args:
            account (_type_): 账户id
            start_date (_type_, optional): _description_. Defaults to None.
            end_date (_type_, optional): _description_. Defaults to None.
            mar_goal (int, optional): 1 商品 2 直播间
        """
        url = "https://qianchuan.jinritemai.com/ad/api/pmc/v1/standard/get_summary_info"
        today = date.today()
        if start_time is None:
            start_time = today.strftime('%Y-%m-%d 00:00:00')
        if end_time is None:
            end_time = today.strftime('%Y-%m-%d 23:59:59')
        params = {
            "aavid": account,
            "gfversion": "1.0.0.5980",
        }
        data = {
            "campaign_type": 1,
            "start_time": start_time,
            "end_time": end_time,
            "smart_bid_type": -1,
            "ad_status_filter_type": 0,
            "auto_manage_filter": 0,
            "order_by_type": 2,
            # "order_by_field": "stat_cost",
            "page": 1,
            "page_size": 10,
            "need_diagnosis": 2,
            "campaign_scene": "-1",
            "need_diagnosis_all": 2,
            "need_quota": 0,
            "ad_cost_status": -1,
            "needHeatingTool": False,
            "external_action_config": "",
            "optimize_period": -1,
            "ad_iDs": "",
            "dataSetKey": "cm_prom_room_feed",
            "aavid": account
        }
        # 设置搜索条件
        if search_key:
            data['search_type'] = 1
            data['search_keyword'] = search_key

        if mar_goal == 1:
            # 商品
            data['mar_goal'] = 1
            data['dataSetKey'] = 'common_promotion_product_feed'
            data['metrics'] = "stat_cost,show_cnt,ctr,click_cnt,cpm_platform,all_order_pay_count_7days,all_order_pay_gmv_7days,pay_order_count,pay_order_amount,prepay_and_pay_order_roi"
        elif mar_goal == 2:
            # 直播间
            data['mar_goal'] = 2
            data['dataSetKey'] = 'cm_prom_room_feed'
            data['metrics'] = "stat_cost,prepay_and_pay_order_roi,show_cnt,ctr,click_cnt,cpm_platform,all_order_prepay_and_pay_roi_7days,all_order_pay_count_7days,all_order_pay_gmv_7days,pay_order_count,pay_order_amount,qianchuan_first_order_cnt,qianchuan_first_order_direct_pay_order_roi,qianchuan_first_order_roi30,qianchuan_author_first_order_direct_pay_order_roi,qianchuan_author_first_order_cnt,qianchuan_brand_first_order_cnt,qianchuan_brand_first_order_direct_pay_order_roi"
        headers = {
            "cookie": self.cookies,
            "x-csrftoken": self.csrf_token,  # 必要
        }
        response = self.session.request(
            "POST", url, json=data, headers=headers, params=params)
        return response.json()['data']['totalMetrics']['metrics']

    def getLiveRoomTotalRatio(self, aavid, anchor_ids, end_time=None):
        url = 'https://qianchuan.jinritemai.com/ad/ecom/marketing/api/v1/todayLive/getLiveRoomTotalRatio'
        last_start_time = datetime.strptime(
            end_time, '%Y-%m-%d')-timedelta(days=1)
        last_start_time = last_start_time.strftime('%Y-%m-%d')
        params = {
            "start_time": f"{end_time} 00:00:00",
            "end_time": f"{end_time} 23:59:59",
            "ratio_metrics": "stat_cost,luban_live_pay_order_gpm,cpc_platform,ecp_convert_rate,ecp_cpa_platform,live_pay_order_gmv_alias,luban_live_pay_order_gmv,ad_live_prepay_and_pay_order_gmv_roi,live_create_order_count_alias,luban_live_order_count,live_pay_order_count_alias,luban_live_pay_order_count,live_pay_order_gmv_avg,luban_live_prepay_order_count,live_prepay_order_count_alias,live_order_refund_count_7d",
            "stats_authority": "-1",
            "page_type": "16000",
            "gfversion": "1.0.1.5835",
            "anchor_ids": anchor_ids,
            "aavid": aavid,
            "last_start_time": f"{last_start_time} 00:00:00",
            "last_end_time": f"{last_start_time} 23:59:59"
        }
        headers = {
            "cookie": self.cookies,
        }

        rsp = self.session.get(url, headers=headers, params=params)
        print(rsp.json())

    def standard_get_summary_info(self, account, start_date=None, end_date=None, page=1, page_size=10, mar_goal=1):
        """获取账户汇总信息 (用于获取商品页数)

        Args:
            account (_type_): 账户id
            start_date (_type_, optional): _description_. Defaults to None.
            end_date (_type_, optional): _description_. Defaults to None.
            mar_goal: 1 商品 2 直播间
        """
        url = "https://qianchuan.jinritemai.com/ad/api/pmc/v1/standard/get_summary_info"
        params = {
            "aavid": account,
            "gfversion": "1.0.0.5198",
        }
        data = {
            "mar_goal": mar_goal,
            "campaign_type": 1,
            "start_time": start_date,
            "end_time": end_date,
            'create_start_date': start_date[:10],
            'create_end_date': end_date[:10],
            "smart_bid_type": -1,
            "ad_status_filter_type": 0,
            "auto_manage_filter": 0,
            "metrics": "stat_cost,show_cnt,ctr,click_cnt,cpm_platform,all_order_pay_count_7days,all_order_pay_gmv_7days,pay_order_count,pay_order_amount,prepay_and_pay_order_roi",
            "order_by_type": 2,
            "page": page,
            "page_size": page_size,
            "need_diagnosis": 2,
            "campaign_scene": "-1",
            "need_diagnosis_all": 2,
            "need_quota": 0,
            "ad_cost_status": -1,
            "needHeatingTool": False,
            "external_action_config": "",
            "optimize_period": -1,
            "ad_iDs": "",
            "dataSetKey": "common_promotion_product_feed",
            "aavid": account
        }
        headers = {
            "cookie": self.cookies,
            "x-csrftoken": self.csrf_token,  # 必要
        }
        response = self.session.request(
            "POST", url, json=data, headers=headers, params=params)
        return response.json()
   
    def standard_get_summary_info_全域(self, account, start_date=None, end_date=None, page=1, page_size=10, mar_goal=1):
        """获取账户汇总信息-全域

        Args:
            account (_type_): 账户id
            start_date (_type_, optional): _description_. Defaults to None.
            end_date (_type_, optional): _description_. Defaults to None.
            mar_goal: 1 商品 2 直播间
        
        return rsp['data']['totalMetrics']['metrics'] # 消耗:['statCostForRoi2']['value'] 成交:['totalPayOrderGmvForRoi2']['value']
        """
        url = "https://qianchuan.jinritemai.com/ad/api/pmc/v1/standard/get_summary_info"
        params = {
            "aavid": account,
            "gfversion": "1.0.0.6954",
        }
        data={
            "mar_goal": mar_goal,
            "dataSetKey": "product_roi2_promotion",
            "page": page,
            "page_size": page_size,
            "order_by_type": 2,
            "order_by_field": "create_time",
            "start_time": f"{start_date}",
            "end_time": f"{end_date}",
            "smartBidType": 0,
            "ad_cost_status": -1,
            "ad_status_filter_type": 0,
            "metrics": "stat_cost_for_roi2,stat_cost_for_roi2_primary,total_pay_order_count_for_roi2,total_pay_order_count_for_roi2_primary,total_pay_order_gmv_for_roi2,total_pay_order_gmv_for_roi2_primary,total_prepay_and_pay_order_roi2,total_prepay_and_pay_order_roi2_primary,total_cost_per_pay_order_for_roi2,total_cost_per_pay_order_for_roi2_primary,total_pay_order_coupon_amount_for_roi2,total_pay_order_coupon_amount_for_roi2_primary",
            "adlab_mode": 1,
            "smart_bid_type": 0,
            "aavid": account
        }
        headers = {
            "cookie": self.cookies,
        }
        response = self.session.request(
            "POST", url, json=data, headers=headers, params=params)
        return response.json()

    def list_ads_optional(self, account,sid):
        """获取账户推广商品数据2
            竞价推广-推广商品-通用广告-计划
            # 目前和list_ads_required 不同的地方是hintType

        Args:
            account (str): 账户
            sid (str): 从list_ads_required 获取
        """
        url = "https://qianchuan.jinritemai.com/ad/api/pmc/v1/list_ads_optional"
        params = {
            "aavid": account,
            "gfversion": "1.0.0.7088",
            'modules':'4,12,1,2,14,13,16,9,26,17,254',
            'sid':sid
        }
        headers = {
            "cookie": self.cookies,
        }
        response = self.session.request(
            "GET", url, headers=headers, params=params)
        return response.json()

    def list_ads_material_list(self,account,adid,startTime,endTime):
        """获取账户推广商品数据
            竞价推广-推广商品-通用广告-计划-- 点进账户后，点击创意，点击视频

        Args:
            account (str): 账户
            adid (str): 广告id
            startTime (str): 开始时间
            endTime (str): 结束时间
        """
        url = "https://qianchuan.jinritemai.com/ad/api/pmc/v1/ad/material/list"
        params={
            "aavid": "****************",
            "gfversion": "1.0.0.7088"
        }
        data={
            "metrics": "stat_cost,show_cnt,ctr,click_cnt,cpm_platform,ecp_convert_cnt,ecp_convert_rate,ecp_cpa_platform,all_order_create_roi_7days,all_order_prepay_and_pay_roi_7days,all_order_pay_count_7days,all_order_pay_gmv_7days,pay_order_count,pay_order_amount,prepay_and_pay_order_roi,create_order_count,create_order_amount,create_order_roi,prepay_order_count,prepay_order_amount,live_pay_order_cost_per_order,create_order_coupon_amount,pay_order_coupon_amount,unfinished_estimate_order_gmv,indirect_order_create_count_7days,indirect_order_create_gmv_7days,indirect_order_pay_count_7days,indirect_order_pay_gmv_7days,indirect_order_prepay_count_7days,indirect_order_prepay_gmv_7days,indirect_order_unfinished_estimate_gmv_7days,dy_follow,dy_share,dy_comment,dy_like,total_play,play_duration_3s,play_25_feed_break,play_50_feed_break,play_75_feed_break,play_over,play_over_rate,luban_live_enter_cnt,live_watch_one_minute_count,live_fans_club_join_cnt,luban_live_slidecart_click_cnt,luban_live_click_product_cnt,luban_live_comment_cnt,luban_live_share_cnt,luban_live_gift_cnt,luban_live_gift_amount",
            "materialType": 3,
            "adId": adid,
            "sources": "",
            "imageModes": "",
            "campaignScene": 0,
            "marGoal": 2,
            "campaignType": 1,
            "page": 1,
            "pageSize": 20,
            "havingCost": "1",
            "orderType": 2,
            "orderField": "",
            "startTime": startTime,
            "endTime": endTime,
            "pmcMaterialAnalysisTypes": "",
            "dataSetKey": "cm_prom_room_feed_daily_material_videoAtitle",
            "aavid": account
        }
        headers = {
            "cookie": self.cookies,
        }
        response = self.session.request(
            "POST", url, json=data, headers=headers, params=params)
        return response.json()
    
    def list_ads_required(self, account, start_date=None, end_date=None,create_start_date=None,create_end_date=None, page=1, page_size=100,ad_status_filter_type=0):
        """获取账户推广商品数据
            竞价推广-推广商品-通用广告-计划
            ad_status_filter_type 0:全部 16:审核未通过
        Args:
            account (str): 账户
            start_date (str, optional): 开始日期. Defaults to None. "2024-04-08 00:00:00"
            end_date (str, optional): 结束日期. Defaults to None. "2024-04-08 23:59:59"
        """
        url = "https://qianchuan.jinritemai.com/ad/api/pmc/v1/list_ads_required"
        params = {
            "aavid": account,
            "gfversion": "1.0.0.7088",
        }
        data = {
            "modules": "6,7,8,11",
            "needRequestOptional": 1,
            "discardTotalNum": 1,
            "listAdsFromBusiness": 1,
            "mar_goal": 2,
            "campaign_type": 1,
            "start_time": start_date,
            "end_time": end_date,
            "smart_bid_type": -1,
            "ad_status_filter_type": ad_status_filter_type,
            "auto_manage_filter": 0,
            "metrics": "stat_cost,show_cnt,ctr,click_cnt,cpm_platform,all_order_prepay_and_pay_roi_7days,all_order_pay_count_7days,all_order_pay_gmv_7days,pay_order_count,pay_order_amount,prepay_and_pay_order_roi",
            "order_by_type": 2,
            "page": page,
            "page_size": page_size,
            "need_diagnosis": 2,
            "campaign_scene": "-1",
            "need_diagnosis_all": 2,
            "need_quota": 0,
            "ad_cost_status": -1,
            "needHeatingTool": False,
            "external_action_config": [],
            "optimize_period": -1,
            "ad_iDs": "",
            "dataSetKey": "cm_prom_room_feed",
            "aavid": account
        }
        if create_start_date and create_end_date:
            data['create_start_date']=create_start_date
            data['create_end_date']=create_end_date
        headers = {
            "cookie": self.cookies,
            "x-csrftoken": self.csrf_token,  # 必要
        }
        response = self.session.request(
            "POST", url, json=data, headers=headers, params=params)
        # print(response.text)
        return response.json()

    def get_account_video_list(self,account,page=1,pageSize=50):
        """获取账户视频列表
        说明:用于低效素材获取
        """
        url = "https://qianchuan.jinritemai.com/ad/api/creation/material/video-list"
        params={
            "queryString": "",
            "source": "",
            "tags": "",
            "imageModes": "",
            "analysisType": "1", # 低效素材
            "page": page,
            "pageSize": pageSize,
            "orderBy": "2",
            "field": "create_time",
            "aavid": account
        }
        headers = {
            "cookie": self.cookies,
        }
        response = self.session.request(
            "GET", url, headers=headers, params=params)
        return response.json()['data']
    
    
    
    def account_product(self, account, t=0, d='today'):
        """获取账户推广商品数据
        url: https://qianchuan.jinritemai.com/data-report/bidding/common-promotion?aavid=****************
        Args:
            account (str): 账户
            t (int, optional=0): 0:全部 1:千川 2:随心推
        """
        url = "https://qianchuan.jinritemai.com/ad/marketing/data/api/v1/commonPromotion/getCommonPromotionList"

        params = {
            "gfversion": "1.0.1.4960",
            "aavid": account,
            "verifyFp": self.fp,
            "fp": self.fp,
            "msToken": self.csrf_token,
            # "a_bogus": "Ov4DhOZPMsm1HTZBqXkz9CzO-3L0YW-sgZEzK5fuFzou"
        }

        data = {
            "page_type": 12103,
            "Metrics": ["stat_cost", "show_cnt", "ctr", "click_cnt", "cpm", "all_order_pay_count_7days", "all_order_pay_gmv_7days", "pay_order_amount", "prepay_and_pay_order_roi", "pay_order_count", "qianchuan_first_order_cnt", "qianchuan_first_order_direct_pay_order_roi", "qianchuan_first_order_roi30", "qianchuan_author_first_order_direct_pay_order_roi", "qianchuan_author_first_order_cnt", "qianchuan_brand_first_order_cnt", "qianchuan_brand_first_order_direct_pay_order_roi"],
            "Dimensions": ["qianchuan_product_id"],
            "SearchParameter": {
                "TimeFilter": {
                    "StartTime": self.start_date,
                    "EndTime": self.end_date
                },
                "Search": {
                    "Keyword": "",
                    "Type": 3
                },
                "OrderBy": [
                    {
                        "Type": 2,
                        "Field": "stat_cost"
                    }
                ],
                "PageParams": {
                    "Page": 1,
                    "PageSize": 100
                }
            },

            "Hints": {
                "unitConvert": "1",
                "setMetric": "1"
            },
            "aavid": account
        }
        if t == 0:
            data['CruxStatsFilter'] = {
                "smart_bid_type": [0, 7],
                "ad_status_filter_type": [30],
                "advertiser_id": [account],
                "marketing_goal": [1],
                "extra_map": {"partitionType": ["Day"]},
                "pricing_category": [2],
                "adlab_mode": ["0"],
                "campaign_type_view": [1, 2, 1001]}
        elif t == 1:
            data['CruxStatsFilter'] = {
                "ecp_app_id": [1],
                "smart_bid_type": [0, 7],
                "ad_status_filter_type": [30],
                "advertiser_id": [account],
                "marketing_goal": [1],
                "extra_map": {"partitionType": ["Day"]}, "pricing_category": [2], "adlab_mode": ["0"], "campaign_type_view": [1, 2, 1001]
            }
        elif t == 2:
            data['CruxStatsFilter'] = {
                "ecp_app_id": [2],
                "advertiser_id": [account],
                "marketing_goal": [1], "extra_map": {"partitionType": ["Day"]}, "pricing_category": [2], "adlab_mode": ["0"]
            }
        headers = {
            "cookie": self.cookies,
            "x-csrftoken": self.csrf_token,  # 必要
        }

        response = self.session.request(
            "POST", url, json=data, headers=headers, params=params)
        products = response.json()
        print(products)
        if products['message'] != 'success':
            print(products)
            raise Exception('接口错误')
        products = products['data']['StatsData']['StatsDataRows']
        rows = []
        
        if d == 'today':
            d = date.today().strftime('%Y-%m-%d')
        else:
            d = (date.today()-timedelta(days=1)).strftime('%Y-%m-%d')
        for product in products:
            rows += [[account, product['Dimensions']['qianchuan_product_id'], x['Metrics']['stat_cost']['Value'], x['Metrics']
                      ['pay_order_amount']['Value']] for x in product['SupplementInfo']['StatsDataRows'] if x['Dimensions']['stat_time_day'] == d]
        return rows

    def account_info(self, aavid, anchor_id, d='today'):
        """获取账户推广商品数据
        url: https://qianchuan.jinritemai.com/data-report/bidding/site-promotion?aavid=****************
        Args:
            account (str): 账户
        """
        url = "https://qianchuan.jinritemai.com/ad/marketing/data/api/v1/common/dataOverviewSimpleQueryStat"

        params = {
            "gfversion": "1.0.1.5328",
            "aavid": aavid,
            "verifyFp": self.fp,
            "fp": self.fp,
            "msToken": self.csrf_token,
            # "a_bogus": "Ov4DhOZPMsm1HTZBqXkz9CzO-3L0YW-sgZEzK5fuFzou"
        }
        headers = {
            "cookie": self.cookies,
        }
        data = {
            "PageType": 60030,
            "Metrics": [
                "stat_cost",
                "total_prepay_and_pay_order_roi2",
                "total_pay_order_gmv_for_roi2",
                "total_pay_order_count_for_roi2",
                "total_cost_per_pay_order_for_roi2",
                "total_pay_order_coupon_amount_for_roi2"
            ],
            "Dimensions": [
                "adlab_mode"
            ],
            "SearchParameter": {
                "TimeFilter": {
                    "StartTime":  datetime.now().strftime('%Y-%m-%d 00:00:00'),
                    "EndTime":  datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                },
                "AdFilter": {
                    "MarGoal": 10
                }
            },
            "CruxStatsFilter": {
                "advertiser_id": [
                    aavid
                ],
                "anchor_id": [
                    anchor_id
                ],
                "pricing_category": [
                    2
                ],
                "marketing_goal": [
                    2
                ],
                "ecp_app_id": [
                    1
                ],
                "campaign_type": [
                    1
                ],
                "adlab_mode": [
                    "1"
                ]
            },
            "Hints": {
                "unitConvert": "1",
                "setMetric": "1"
            },
            # "NeedOriginResp": True,
            "aavid": aavid
        }

        headers = {
            "cookie": self.cookies,
            "x-csrftoken": self.csrf_token,  # 必要
        }

        response = self.session.request(
            "POST", url, json=data, headers=headers, params=params)
        products = response.json()
        if products['message'] != 'success':
            print(products)
            raise Exception('接口错误')
        products = products['data']['statsData']['statsDataRows']

        return {products[0]['metrics'][x]['metricsZh']: products[0]['metrics'][x]['value'] for x in products[0]['metrics']}
        # if d == 'today':
        #     d = date.today().strftime('%Y-%m-%d')
        # else:
        #     d = (date.today()-timedelta(days=1)).strftime('%Y-%m-%d')
        # for product in products:
        #     rows += [[aavid, product['Dimensions']['qianchuan_product_id'], x['Metrics']['stat_cost']['Value'], x['Metrics']
        #               ['pay_order_amount']['Value']] for x in product['SupplementInfo']['StatsDataRows'] if x['Dimensions']['stat_time_day'] == d]
        # return rows

    def account_data_biaozhu(self,aavid,start_date,end_date,time_type='hour'):
        """获取账户标准推广
        路径: 数据-标准推广-抖音号
        url: https://qianchuan.jinritemai.com/data/report?aavid=****************&x_tt_random=*************
        Args:
        """
        url = "https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery"
        params={
            "reqFrom": "cm_overview_compare",
            "gfversion": "1.0.1.6659",
            'aavid': aavid
        }
        headers={
            "cookie": self.cookies,
            "x-csrftoken": self.csrf_token,  # 必要
        } 
        with open(r'config\千川\数据_标准推广_抖音号','r',encoding='utf-8') as f:
            data=json.load(f)
        data['aavid']=aavid
        data['Filters']['Conditions'][0]['Values']=[aavid]
        
        data['StartTime']=start_date.strftime('%Y-%m-%d %H:%M:%S')
        data['EndTime']=end_date.strftime('%Y-%m-%d %H:%M:%S')
        del data['ComparisonParams']
        if time_type=='day':
            data['Dimensions'][0]='stat_time_day'
        else:
            data['Dimensions'][0]='stat_time_hour'
        response=self.session.post(url,params=params,json=data,headers=headers)
        response=response.json()
        print(response['status_code'],response['message'])
        return response['data']['StatsData'] # ['Rows']
        
    def account_data_biaozhu2(self,aavid,start_date,end_date,page=1,page_size=100):
        """获取账户标准推广
        路径: 数据-标准推广-抖音号
        url: https://qianchuan.jinritemai.com/data/report?aavid=****************&x_tt_random=*************
        Args:
        """
        url = "https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery"
        params={
            "reqFrom": "cm_table",
            "gfversion": "1.0.0.3420",
            'aavid': aavid
        }
        headers={
            "cookie": self.cookies,
            "x-csrftoken": self.csrf_token,  # 必要
        } 
        with open(r'config\千川\数据_标准推广_抖音号2.json','r',encoding='utf-8') as f:
            data=json.load(f)
        data['aavid']=aavid
        data['Filters']['Conditions'][0]['Values']=[aavid]
        data['PageParams']['Limit']=page_size
        data['PageParams']['Offset']=(page-1)*page_size
        data['StartTime']=start_date.strftime('%Y-%m-%d %H:%M:%S')
        data['EndTime']=end_date.strftime('%Y-%m-%d %H:%M:%S')
        response=self.session.post(url,params=params,json=data,headers=headers)
        response=response.json()
        return response['data']['StatsData'] # ['Rows']
        
    def account_data_quanyu2(self,aavid,start_date,end_date,page=1,page_size=100,fittle_anchor_id=None):
        """获取账户全域推广
        路径: 数据-全域推广-抖音号
        url: https://qianchuan.jinritemai.com/dataV2/bidding/site-promotion?aavid=****************
        Args:
        """
        url = "https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery"
        params={
            "reqFrom": "listTable",
            "gfversion": "1.0.0.3496",
            'aavid': aavid
        }
        headers={
            "cookie": self.cookies,
            "x-csrftoken": self.csrf_token,  # 必要
        } 
        with open(r'config\千川\数据_全域推广_抖音号 copy.json','r',encoding='utf-8') as f:
            data=json.load(f)
        data['aavid']=aavid
        data['Filters']['Conditions'][0]['Values']=[aavid]
        data['PageParams']['Limit']=page_size
        data['PageParams']['Offset']=(page-1)*page_size
        data['StartTime']=start_date.strftime('%Y-%m-%d %H:%M:%S')
        data['EndTime']=end_date.strftime('%Y-%m-%d %H:%M:%S')
        if fittle_anchor_id:
            data['Filters']['Conditions'].append({
            "Field": "anchor_id",
            "Operator": 7,
            "Values": [
                fittle_anchor_id
            ]
        })
        # print(data)
        response=self.session.post(url,params=params,json=data,headers=headers)
        response=response.json()
        return response['data']['StatsData'] # ['Rows']        
    
    def account_data_quanyu(self,aavid,room_id,start_date,end_date,time_type='hour',数据维度='抖音号'):
        """获取账户全域数据
        路径: 数据-全域推广-直播间
        url: https://qianchuan.jinritemai.com/data/bidding/site-promotion?aavid=****************&x_tt_random=*************
        Args:
        """
        url = "https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery"
        params={
            "reqFrom": "uniOverviewTrendchart",
            "gfversion": "1.0.1.6659",
            "aavid": aavid
        }
        headers={
            "cookie": self.cookies,
            "x-csrftoken": self.csrf_token,  # 必要
        } 
        with open(r'config\千川\数据_全域推广','r',encoding='utf-8') as f:
            data=json.load(f)
        data['aavid']=aavid
        data['Filters']['Conditions'][0]['Values']=[aavid]
        
        data['StartTime']=start_date.strftime('%Y-%m-%d %H:%M:%S')
        data['EndTime']=end_date.strftime('%Y-%m-%d %H:%M:%S')
        del data['ComparisonParams']
        if time_type=='day':
            data['Dimensions'][0]='stat_time_day'
        else:
            data['Dimensions'][0]='stat_time_hour'
        # 数据维度
        # 抖音号
        if 数据维度=='抖音号':
            data['DataSetKey']='site_promotion_post_overview'
            data['Filters']['Conditions'][3]['Values']=[room_id] # 这个是anchor_id
            data['Filters']['Conditions'][3]['Field']='anchor_id'
        elif 数据维度=='直播间':
            data['DataSetKey']='site_promotion_post_overview_living'
            data['Filters']['Conditions'][3]['Values']=[room_id]
            data['Filters']['Conditions'][3]['Field']='room_id'
        elif 数据维度=='素材':
            data['DataSetKey']='site_promotion_post_data_live'
            data['Filters']['Conditions'][3]['Values']=[room_id]
            data['Filters']['Conditions'][3]['Field']='room_id'
        response=self.session.post(url,params=params,json=data,headers=headers)
        response=response.json()
        print(response['status_code'],response['message'])
        return response['data']['StatsData'] # ['Rows']
        
    
    def account_info_quanyu(self, aavid, d='today', t_date=None):
        """获取账户全域数据
        url: https://qianchuan.jinritemai.com/data-report/bidding/site-promotion?aavid=****************
        Args:
        """
        url = "https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery?reqFrom=unitable&gfversion=1.0.1.6052&aavid="+aavid
        if d == 'today' and t_date is None:
            start_date = datetime.now().strftime('%Y-%m-%d 00:00:00')
            end_date = datetime.now().strftime('%Y-%m-%d 23:59:59')
        elif t_date:
            start_date = t_date.strftime('%Y-%m-%d 00:00:00')
            end_date = t_date.strftime('%Y-%m-%d 23:59:59')
        else:
            day = datetime.now()-timedelta(days=1)
            start_date = day.strftime('%Y-%m-%d 00:00:00')
            end_date = day.strftime('%Y-%m-%d 23:59:59')
        payload = {
            "refer": "ecp,7248899320223694906,7289039994382024762,site_promotion_post_overview",
            "DataSetKey": "site_promotion_post_overview",
            "Metrics": [
                "stat_cost",
                "total_prepay_and_pay_order_roi2",
                "total_pay_order_gmv_for_roi2",
                "total_pay_order_count_for_roi2",
                "total_cost_per_pay_order_for_roi2",
                "total_prepay_order_count_for_roi2",
                "total_prepay_order_gmv_for_roi2"
            ],
            "Dimensions": [
                "anchor_name",
                "anchor_show_id",
                "anchor_icon"
            ],
            "DrillDimensions": [
                [
                    "stat_time_day"
                ]
            ],
            "Filters": {
                "ConditionRelationshipType": 1,
                "Conditions": [
                    {
                        "Field": "advertiser_id",
                        "Operator": 7,
                        "Values": [
                            aavid
                        ]
                    },
                    {
                        "Field": "marketing_goal",
                        "Operator": 7,
                        "Values": [
                            "2"
                        ]
                    },
                    {
                        "Field": "fill_stat_time",
                        "Operator": 7,
                        "Values": [
                            "off"
                        ]
                    },
                    {
                        "Field": "adlab_mode_fork",
                        "Operator": 7,
                        "Values": [
                            "1"
                        ]
                    }
                ]
            },
            "StartTime": start_date,
            "EndTime": end_date,
            "PageParams": {
                "Limit": "10",
                "Offset": "0"
            },
            "aavid": aavid
        }
        headers = {
            "cookie": self.cookies,
            "x-csrftoken": self.csrf_token,  # 必要
        }
        response = requests.request(
            "POST", url, headers=headers, json=payload)
        data = response.json()
        try:
            data = [x for x in data['data']['StatsData']['Rows']]
            if not data:
                return None
            data = data[0]
            return {'stat_cost': data['Metrics']['stat_cost']['Value'], 'gmv': data['Metrics']['total_pay_order_gmv_for_roi2']['Value'], 'roi': data['Metrics']['total_prepay_and_pay_order_roi2']['Value']}
        except:
            return None

    def account_info_standard_2(self, aavid, get_type, search_key=None, start_date=None, end_date=None):
        """获取账户标准数据

        Args:
            aavid (_type_): aavid
            get_type (_type_): 广告组,直播间,计划
            search_key (_type_, optional): _description_. Defaults to None.
            start_date (_type_, optional): _description_. Defaults to None.
            end_date (_type_, optional): _description_. Defaults to None.

        Returns:
            dict: {'stat_cost': 18046.77, 'pay_order_amount': 17916.07, 'pay_order_coupon_amount': 1153.03}
        """
        url = 'https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery'
        headers = {
            'cookie': self.cookies
        }
        params = {
            "reqFrom": "cm_overview_compare",
            "gfversion": "1.0.1.6301",
            "aavid": aavid
        }
        if not start_date:
            start_date = datetime.now().strftime('%Y-%m-%d 00:00:00')
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        last_date_start = datetime.strptime(
            start_date, '%Y-%m-%d %H:%M:%S')-timedelta(days=1)
        last_date_end = datetime.strptime(
            end_date, '%Y-%m-%d %H:%M:%S')-timedelta(days=1)
        last_date_start = last_date_start.strftime('%Y-%m-%d 00:00:00')
        last_date_end = last_date_end.strftime('%Y-%m-%d 23:59:59')
        data = {
            "Dimensions": [
                "stat_time_day"
            ],
            "Filters": {
                "ConditionRelationshipType": 1,
                "Conditions": [
                    {
                        "Field": "advertiser_id",
                        "Operator": 7,
                        "Values": [
                            aavid
                        ]
                    },
                    {
                        "Field": "pricing_category",
                        "Operator": 7,
                        "Values": [
                            "2"
                        ]
                    },
                    {
                        "Field": "marketing_goal",
                        "Operator": 7,
                        "Values": [
                            "2"
                        ]
                    },
                    {
                        "Field": "fill_stat_time",
                        "Operator": 7,
                        "Values": [
                            "on"
                        ]
                    },
                    {
                        "Field": "ignore_zero_dimension",
                        "Operator": 7,
                        "Values": [
                            "on"
                        ]
                    }
                ]
            },
            "StartTime": start_date,
            "EndTime": end_date,
            "ComparisonParams": {
                "RatioStartTime": last_date_start,
                "RatioEndTime": last_date_end
            },
            "aavid": aavid
        }

        if get_type == "广告组":
            data['Metrics'] = 'stat_cost,pay_order_amount,pay_order_coupon_amount'.split(
                ',')
            data["DataSetKey"] = "cm_prom_room_feed"
            广告组 = [{
                "Field": "ecp_is_adlab",
                "Operator": 8,
                "Values": [
                    "1"
                ]
            },
                {
                    "Field": "ad_status",
                    "Operator": 7,
                    "Values": [
                        "30"
                    ]
            }]
            if search_key:
                广告组.append(
                    {
                        "Field": "campaign_name",
                        "Operator": 7,
                        "Values": [
                            search_key
                        ]
                    }
                )
            data['Filters']['Conditions'] += 广告组
        elif get_type == "直播间":
            data['Metrics'] = 'stat_cost,pay_order_amount,pay_order_coupon_amount'.split(
                ',')
            data["DataSetKey"] = "cm_prom_room_feed_room"
        elif get_type == "计划":
            data['Metrics'] = 'stat_cost,pay_order_amount,pay_order_coupon_amount'.split(
                ',')
            data["DataSetKey"] = "cm_prom_room_feed"
            计划 = [
                {
                    "Field": "ad_status",
                    "Operator": 7,
                    "Values": [
                        "30"
                    ]
                }]
            if search_key:
                计划.append(
                    {
                        "Field": "ad_name",
                        "Operator": 7,
                        "Values": [
                            search_key
                        ]
                    }
                )
            data['Filters']['Conditions'] += 计划
        for i in range(3):
            try:
                response = self.session.post(
                    url, params=params, json=data, headers=headers)
                response = response.json()
                # {"data":{"StatsData":{}},"status_code":0,"message":"success"}
                if not response['data']['StatsData']:
                    return None
                return response['data']['StatsData']['Rows'][0]['Metrics']
            except:
                print(response.text)
                print(f'{aavid}:获取数据失败，重试中...')
                time.sleep(1)
                continue
        return None

    def account_info_standard(self, aavid, product_id, d='today'):
        url = f"https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery"
        params = {
            'aavid': aavid,
            'gfversion': '1.0.1.5912',
        }
        if d == 'today':
            start_date = datetime.now().strftime('%Y-%m-%d 00:00:00')
            end_date = datetime.now().strftime('%Y-%m-%d 23:59:59')
        else:
            day = datetime.now()-timedelta(days=1)
            start_date = day.strftime('%Y-%m-%d 00:00:00')
            end_date = day.strftime('%Y-%m-%d 23:59:59')
        # print(start_date, end_date)
        data = {
            "DataSetKey": "common_promotion_product_feed",
            "Metrics": [
                "stat_cost",
                "ecp_convert_cnt",
                "pay_order_amount",
                "show_cnt",
                "click_cnt",
                "dy_follow",
                "qianchuan_first_order_direct_pay_gmv",
                "qianchuan_first_order_cnt"
            ],
            "Dimensions": [
                "qianchuan_product_id",
                "qianchuan_product_name",
                "qianchuan_product_image"
            ],
            "DrillDimensions": [
                [
                    "stat_time_day"
                ]
            ],
            "Filters": {
                "ConditionRelationshipType": 1,
                "Conditions": [
                    {
                        "Field": "advertiser_id",
                        "Operator": 7,
                        "Values": [
                            aavid
                        ]
                    },
                    {
                        "Field": "pricing_category",
                        "Operator": 7,
                        "Values": [
                            "2"
                        ]
                    },
                    {
                        "Field": "marketing_goal",
                        "Operator": 7,
                        "Values": [
                            "1"
                        ]
                    },
                    {
                        "Field": "ignore_all_metric_are_zero",
                        "Operator": 7,
                        "Values": [
                            "on"
                        ]
                    },
                    {
                        "Field": "ignore_zero_dimension",
                        "Operator": 7,
                        "Values": [
                            "on"
                        ]
                    },
                    {
                        "Field": "ad_status",
                        "Operator": 7,
                        "Values": [
                            "30"
                        ]
                    },
                    {
                        "Field": "qianchuan_product_name",
                        "Operator": 7,
                        "Values": [
                            ""
                        ]
                    }
                ]
            },
            "StartTime": start_date,
            "EndTime": end_date,
            "OrderBy": [
                {
                    "Type": 2,
                    "Field": "stat_cost"
                }
            ],
            "PageParams": {
                "Limit": "10",
                "Offset": "0"
            },
            # "refer": "ecp,72890390004032307787281254696247885861common_promotion_product_feed",
            "aavid": aavid
        }

        headers = {
            "cookie": self.cookies,
            "x-csrftoken": self.csrf_token,  # 必要
        }

        response = requests.request(
            "POST", url, params=params, headers=headers, json=data)
        data = response.json()
        print(data)
        try:
            data = [x for x in data['data']['StatsData']['Rows']
                    if x['Dimensions']['qianchuan_product_id']['Value'] == product_id]
            if not data:
                return None
            data = data[0]
            return {'id': data['Dimensions']['qianchuan_product_id']['Value'], 'stat_cost': data['Metrics']['stat_cost']['Value'], 'pay_order_amount': data['Metrics']['pay_order_amount']['Value']}
        except:
            return None

    def get_douyin_userId(self,uniqueId):
        """获取抖音号/nickname的userId
        
        aavid=**************** # 这个没有实际意义所以默认即可
        """

        url = "https://qianchuan.jinritemai.com/ad/api/v1/account/get-douyin-account?aavid=****************&gfversion=1.0.1.6465"

        data = {
        "uniqueId": uniqueId
        }
        headers = {
            'Cookie': self.cookies,
        }

        response = requests.request("POST", url, headers=headers, json=data)
        data = response.json()
        if data['message'] == '该抖音用户不存在':
            print(f'{uniqueId}:该抖音用户不存在')
            return None
        # print(response.text)
        return response.json()['data']['userId']

    
    
    def get_qianchuan_product_data(self, aavid, start_time, end_time):
        """获取千川全域推广数据-推商品数据
        
        Args:
            aavid: 广告主ID
            start_time: 开始时间 格式:2024-12-07 00:00:00
            end_time: 结束时间 格式:2024-12-07 23:59:59
            
        Returns:
            [
                {
                    'product_id': '商品ID',
                    'product_show_count': '商品展示数',
                    'product_click_count': '商品点击数',
                    'product_cvr_rate': '商品转化率',
                    'product_convert_rate': '商品转化率',
                    'stat_cost': '消耗',
                    'total_pay_order_count': '支付订单数',
                    'total_pay_order_gmv': '支付金额',
                    'total_prepay_and_pay_order_roi': 'ROI',
                    'total_cost_per_pay_order': '客单价',
                    'total_pay_order_coupon_amount': '优惠券金额'
                }
            ]
        """
        url = "https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery"
        
        params = {
            "reqFrom": "listTable",
            "gfversion": "1.0.0.3244",
            "aavid": aavid
        }
        
        data = {
            "DataSetKey": "site_promotion_product_product",
            "Metrics": [
                "product_show_count_for_roi2",
                "product_click_count_for_roi2", 
                "product_cvr_rate_for_roi2",
                "product_convert_rate_for_roi2",
                "stat_cost",
                "total_pay_order_count_for_roi2",
                "total_pay_order_gmv_for_roi2",
                "total_prepay_and_pay_order_roi2",
                "total_cost_per_pay_order_for_roi2",
                "total_pay_order_coupon_amount_for_roi2"
            ],
            "Dimensions": [
                "qianchuan_product_id",
                "qianchuan_product_info"
            ],
            "DrillDimensions": [
                ["stat_time_day"],
                ["range_stat_time_hour"]
            ],
            "Filters": {
                "ConditionRelationshipType": 1,
                "Conditions": [
                    {
                        "Field": "advertiser_id",
                        "Operator": 7,
                        "Values": [aavid]
                    },
                    {
                        "Field": "fill_stat_time",
                        "Operator": 7,
                        "Values": ["off"]
                    },
                    {
                        "Field": "adlab_mode_fork",
                        "Operator": 7,
                        "Values": ["1"]
                    },
                    {
                        "Field": "marketing_goal",
                        "Operator": 7,
                        "Values": ["1"]
                    }
                ]
            },
            "StartTime": start_time,
            "EndTime": end_time,
            "OrderBy": [
                {
                    "Type": 2,
                    "Field": "product_show_count_for_roi2"
                }
            ],
            "PageParams": {
                "Limit": 20,
                "Offset": 0
            },
            "aavid": aavid
        }

        headers = {
            'x-csrftoken': self.csrf_token,
            'cookie': self.cookies,
            'content-type': 'application/json'
        }

        response = requests.post(url, params=params, headers=headers, json=data)
        data = response.json()
        
        result_products = {}
        if data['data']['StatsData']['Rows']:
            for row in data['data']['StatsData']['Rows']:
                product_id=row['Dimensions']['qianchuan_product_id']['Value']
                for hour_row in row['Rows'][0]['Rows']:
                    
                    hour=hour_row['Dimensions']['range_stat_time_hour']['Value']
                    hour=int(hour.split(':')[0])
                    product_show_count=hour_row['Metrics']['product_show_count_for_roi2']['Value']
                    product_click_count=hour_row['Metrics']['product_click_count_for_roi2']['Value']
                    product_cvr_rate=hour_row['Metrics']['product_cvr_rate_for_roi2']['Value']
                    product_convert_rate=hour_row['Metrics']['product_convert_rate_for_roi2']['Value']
                    stat_cost=hour_row['Metrics']['stat_cost']['Value']
                    total_pay_order_count=hour_row['Metrics']['total_pay_order_count_for_roi2']['Value']
                    total_pay_order_gmv=hour_row['Metrics']['total_pay_order_gmv_for_roi2']['Value']
                    total_prepay_and_pay_order_roi=hour_row['Metrics']['total_prepay_and_pay_order_roi2']['Value']
                    total_cost_per_pay_order=hour_row['Metrics']['total_cost_per_pay_order_for_roi2']['Value']
                    total_pay_order_coupon_amount=hour_row['Metrics']['total_pay_order_coupon_amount_for_roi2']['Value']
                    result_products.setdefault(product_id,[])
                    if stat_cost==0 and total_pay_order_coupon_amount+total_pay_order_gmv==0:
                        continue
                    result_products[product_id].append({
                        'hour':hour,
                        'product_show_count':product_show_count,
                        'product_click_count':product_click_count,
                        'product_cvr_rate':product_cvr_rate,
                        'product_convert_rate':product_convert_rate,
                        'stat_cost':stat_cost,
                        'total_pay_order_count':total_pay_order_count,
                        'total_pay_order_gmv':total_pay_order_gmv,
                        'total_prepay_and_pay_order_roi':total_prepay_and_pay_order_roi,
                        'total_cost_per_pay_order':total_cost_per_pay_order,
                        'total_pay_order_coupon_amount':total_pay_order_coupon_amount
                    })
                    
        return result_products
    
    
    
    def bind_author(self,aavid,userid,合作码:str,end_data=None,secsdk=None):
        """绑定合作达人
        url: https://qianchuan.jinritemai.com/account-center/aweme-auth?aavid=****************
        """


        url = f"https://qianchuan.jinritemai.com/ad/api/v1/account/apply-authorization"
        params={
            "aavid": aavid,
            "gfversion": "1.0.1.7006"
        }
        # 如果没有end_data,默认1年后
        if not end_data:
            end_data = (datetime.now()+timedelta(days=365)).strftime('%Y-%m-%d 23:59:59')
            end_data=datetime.strptime(end_data,'%Y-%m-%d %H:%M:%S')
            
        
        end_data=int(end_data.timestamp())
        payload = {
        "authTargetType": 4,
        "authTargetId": userid,
        "scene": 5,
        "code": 合作码,
        "endTime": end_data,
        "aavid": aavid
        }
        
        headers = {
        # 'x-csrftoken': self.csrf_token,
        # 'x-secsdk-csrf-token': secsdk,
        'Cookie': self.cookies,
        'Content-Type': 'application/json'
        }
        # if not secsdk:
        #     head_headers={
        #         'cookie': self.cookies,
        #         'x-secsdk-csrf-request': '1',
        #         'x-secsdk-csrf-version':'1.2.22',
        #     }
            
        #     response=requests.head(url,headers=head_headers)
        #     headers['x-secsdk-csrf-token']=response.headers['x-ware-csrf-token'].split(',')[1]
        #     # print(headers['x-secsdk-csrf-token'])
        # # del headers['Cookie']
        # # print(headers)
        response = requests.request("POST", url,params=params, headers=headers, json=payload)

        # print(response.text)
        return response.json()

    def get_material_crowd_data(self, account:str, material_id:str, start_date:datetime, end_date:datetime):
        """获取素材人群分析数据
        路径: 数据-全域推广-素材分析-视频素材-点击素材-人群分析
        
        Args:
            account (str): 广告主ID
            material_id (str): 素材ID
            start_date (datetime): 开始时间
            end_date (datetime): 结束时间
            
        Returns:
            list: [
                {
                    'city_name': '城市名称',
                    'show_count': '展示数',
                    'click_count': '点击数',
                    'convert_count': '转化数',
                    'convert_rate': '转化率',
                    'stat_cost': '消耗',
                    'pay_order_count': '支付订单数',
                    'pay_order_amount': '支付金额',
                    'roi': 'ROI'
                }
            ]
        """
        url = "https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery"
        params = {
            "gfversion": "1.0.0.3728",
            "aavid": account,
        }
        
        data = json.loads(open('config/千川/数据_全域_素材_人群.json','r',encoding='utf-8').read())
        data['StartTime'] = start_date.strftime('%Y-%m-%d 00:00:00')
        data['EndTime'] = end_date.strftime('%Y-%m-%d 23:59:59')
        data['Filters']['Conditions'][0]['Values'] = [account]
        data['Filters']['Conditions'][1]['Values'] = [material_id]
        data['aavid'] = account
        
        headers = {
            'cookie': self.cookies,
            'content-type': 'application/json',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36',
        }
        params['a_bogus'] = self.generate_a_bogus(json.dumps(params),json.dumps(data),headers['user-agent'])
        response = self.session.post(url, params=params, headers=headers, json=data)
        response_data = response.json()
        print(response_data)
        result = []
        if response_data['data']['StatsData']['Rows']:
            for row in response_data['data']['StatsData']['Rows']:
                result.append({
                    'city_name': row['Dimensions']['city_name']['Value'],
                    'show_count': row['Metrics']['live_show_count_for_roi2_v2']['Value'],
                    'click_count': row['Metrics']['live_click_count_for_roi2_v2']['Value'],
                    'convert_count': row['Metrics']['live_convert_count_for_roi2_v2']['Value'],
                    'convert_rate': row['Metrics']['live_convert_rate_for_roi2_v2']['Value'],
                    'stat_cost': row['Metrics']['stat_cost_for_roi2']['Value'],
                    'pay_order_count': row['Metrics']['total_pay_order_count_for_roi2']['Value'],
                    'pay_order_amount': row['Metrics']['total_pay_order_gmv_for_roi2']['Value'],
                    'roi': row['Metrics']['total_prepay_and_pay_order_roi2']['Value']
                })
                
        return result

    def get_material_analysis(self, account, material_id, p_date):
        """获取素材卖点分析数据
        路径: 数据-全域推广-素材分析-视频素材-点击素材-内容
        Args:
            account (str): 账户id
            material_id (str): 素材id 
            p_date (str): 日期 格式:YYYYMMDD

        Returns:
            dict: 返回素材分析数据
        """
        period_type=30
        assist_type=3
        assist_video_type=2
        url = "https://qianchuan.jinritemai.com/ad/api/data/v1/material-analysis/getContentMaterialAnalysisInfo"
        params = {
            "gfversion": "1.0.0.3728",
            "aavid": account
        }
        
        data = {
            "material_id": material_id,
            "p_date": p_date,
            "period_type": period_type,
            "assist_type": assist_type, 
            "assist_video_type": assist_video_type,
            "aavid": account
        }

        headers = {
            'cookie': self.cookies,
            'content-type': 'application/json',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36'
        }
        
        response = self.session.post(url, params=params, headers=headers, json=data)
        return response.json()
    
    def get_material_crowd(self, account:str, material_id:str, start_date:datetime, end_date:datetime, dimension:str='user_group_label_name'):
        """获取素材人群分析数据
        路径: 数据-全域推广-素材分析-视频素材-点击素材-人群分析

        Args:
            account (str): 账户id
            material_id (str): 素材id
            start_date (datetime): 开始时间
            end_date (datetime): 结束时间
            dimension (str): 维度,可选值:
                city_name 城市名称
                province_name 省份名称
                gender 性别
                age 年龄
                user_group_label_name 人群标签

        Returns:
            dict: 返回素材人群分析数据
        """
        url = "https://qianchuan.jinritemai.com/ad/api/data/v1/common/statQuery"
        params = {
            "gfversion": "1.0.0.3728",
            "aavid": account
        }
        
        data = {
            "DataSetKey": "roi2_video_material_analysis_crow",
            "Metrics":["live_show_count_for_roi2_v2"],
            "Dimensions": [
                dimension
            ],
            "StartTime": start_date.strftime("%Y-%m-%d %H:%M:%S"),
            "EndTime": end_date.strftime("%Y-%m-%d %H:%M:%S"),
            "Filters": {
                "ConditionRelationshipType": 1,
                "Conditions": [
                    {
                        "Field": "advertiser_id",
                        "Values": [
                            account,
                            '****************'
                        ],
                        "Operator": 7
                    },
                    {
                        "Field": "material_id", 
                        "Values": [
                            material_id
                        ],
                        "Operator": 7
                    },
                    {
                        "Field": "marketing_goal",
                        "Values": [
                            "2"
                        ],
                        "Operator": 7
                    },
                    {
                        "Field": "material_type",
                        "Values": [
                            "3"
                        ],
                        "Operator": 7
                    }
                ]
            },
            "OrderBy": [
                {
                    "Type": 2,
                    "Field": "live_show_count_for_roi2_v2"
                }
            ],
            "aavid": account
        }

        headers = {
            'cookie': self.cookies,
            'content-type': 'application/json',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36'
        }
        
        response = self.session.post(url, params=params, headers=headers, json=data)
        return response.json()
    
    def 全域_获取计划素材(self, account:str, ad_id:str):
        """获取全域计划素材详情
        Args:
            account (str): 账户id
            ad_id (str): 计划id
        """
        url = 'https://qianchuan.jinritemai.com/ad/api/creation/v1/ad/adDetail'
        params = {
            'adId': ad_id,
            'aavid': account,
            'deliveryScene': 2,
            'checkUniPromAuthDowngrade': 1
        }
        
        headers = {
            'cookie': self.cookies,
            'content-type': 'application/json;charset=UTF-8',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

        response = self.session.get(url, headers=headers, params=params)
        return response.json()
    
    def 全域_获取达人推广视频(self, account:str, product_id:str, user_id:str, cursor:str='', page_size:int=50):
        """获取达人推广视频列表
        
        Args:
            account (str): 账户id
            product_id (str): 商品id
            user_id (str): 达人id
            cursor (str, optional): 分页游标. Defaults to ''.
            page_size (int, optional): 每页数量. Defaults to 50.
        """
        url = 'https://qianchuan.jinritemai.com/ad/api/creation/material/get-items-by-product'
        
        params = {
            'cursor': cursor,
            'productId': product_id,
            'userId': user_id,
            'sortType': 2,
            'isAwemeSingleVideoAuth': 0,
            'orderBy': 2,
            'field': 'create_time',
            'analysisType': '',
            'pageSize': page_size,
            'aavid': account
        }
        
        headers = {
            'accept': 'application/json, text/plain, */*',
            'content-type': 'application/json',
            'cookie': self.cookies,
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

        response = self.session.get(url, headers=headers, params=params)
        return response.json()

    def 全域_获取预估数据(self, account:str, aweme_id:str, product_ids:str, budget:int=5000, mar_goal:int=1, smart_bid_type:int=0):
        """获取预估数据
        用于-修改计划包用
        Args:
            account (str): 账户id
            aweme_id (str): 达人id
            product_ids (str): 商品id
            budget (int, optional): 预算. Defaults to 5000.
            mar_goal (int, optional): 营销目标. Defaults to 1.
            smart_bid_type (int, optional): 出价类型. Defaults to 0.
        """
        url = 'https://qianchuan.jinritemai.com/ad/api/creation/v1/uni_prom/get-suggested-estimate'
        
        params = {
            'awemeId': aweme_id,
            'marGoal': mar_goal,
            'smartBidType': smart_bid_type,
            'budget': budget,
            'productIds': product_ids,
            'supportMall': 0,
            'lastProductIds': product_ids,
            'aavid': account,
            'gfversion': '1.0.0.4078'
        }
        
        headers = {
            'accept': 'application/json, text/plain, */*',
            'content-type': 'application/json',
            'cookie': self.cookies,
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

        response = self.session.get(url, headers=headers, params=params)
        return response.json()
    
    def 全域_获取计划详情(self, account: str, ad_id: str):
        """获取计划详情
        用于-修改计划包用
        Args:
            ad_id (str): 计划ID
            account (str): 账户ID
        """
        url = 'https://qianchuan.jinritemai.com/ad/api/creation/v1/ad/adDetail'
        
        params = {
            'adId': ad_id,
            'aavid': account,
            'deliveryScene': 2,
            'checkUniPromAuthDowngrade': 1
        }
        
        headers = {
            'Content-Type': 'application/json;charset=UTF-8',
            'cookie': self.cookies,
        }
        url='https://qianchuan.jinritemai.com/ad/api/creation/v1/ad/adDetail'+'?adId='+ad_id+'&aavid='+account+'&deliveryScene=2&checkUniPromAuthDowngrade=1'
        response = self.session.get(url, headers=headers)
        return response.json()
    
    def 获取评论回复列表(self, account: str, comment_id: str, page: int = 1, page_size: int = 10):
        """获取评论回复列表
        
        Args:
            account (str): 账户ID
            comment_id (str): 评论ID
            page (int, optional): 页码. Defaults to 1.
            page_size (int, optional): 每页数量. Defaults to 10.
            
        Returns:
            dict: 评论回复列表数据
        """
        url = 'https://qianchuan.jinritemai.com/ad/api/tool/v1/comment/reply_list'
        
        params = {
            'aavid': account,
            'gfversion': '1.0.1.7332'
        }
        
        data = {
            "commentId": comment_id,
            "requestType": 6,
            "page": page,
            "pageSize": page_size,
            "aavid": account
        }
        
        headers = {
            'accept': 'application/json, text/plain, */*',
            'content-type': 'application/json',
            'cookie': self.cookies,
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36'
        }
        
        response = self.session.post(url, headers=headers, params=params, json=data)
        return response.json()
    
    def 获取评论列表(self, account: str, start_time: str, end_time: str, page: int = 1, page_size: int = 10):
        """获取评论列表
        
        路径: 千川后台-工具-评论内容
        筛选条件：评论层级：一级评论	评论舆情类型：不限	处理状态：不限	隐藏状态：未隐藏
        Args:
            account (str): 账户ID
            start_time (str): 开始时间，格式：YYYY-MM-DD
            end_time (str): 结束时间，格式：YYYY-MM-DD
            page (int, optional): 页码. Defaults to 1.
            page_size (int, optional): 每页数量. Defaults to 10.
            
        Returns:
            dict: 评论列表数据
        """
        url = 'https://qianchuan.jinritemai.com/ad/api/tool/v1/comment/comment_list'
        
        params = {
            'aavid': account,
            'gfversion': '1.0.1.7306'
        }
        
        data = {
            "startTime": start_time,
            "endTime": end_time,
            "content": "",
            "level": 1,
            "hideStatus": 0,
            "commentType": 0,
            "emotionType": 100,
            "itemIds": [],
            "authorIds": [],
            "adIds": [],
            "creativeIds": [],
            "promotionIds": [],
            "requestType": 6,
            "page": page,
            "pageSize": page_size,
            "orderByField": "create_time",
            "orderByType": 1,
            "aavid": account
        }
        
        headers = {
            'accept': 'application/json, text/plain, */*',
            'content-type': 'application/json',
            'cookie': self.cookies,
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36'
        }
        
        response = self.session.post(url, headers=headers, params=params, json=data)
        return response.json()

    def 批量回复评论(self, account: str, comment_ids: list, reply_text: str):
        """批量回复评论
        
        Args:
            account (str): 账户ID
            comment_ids (list): 评论ID列表
            reply_text (str): 回复内容
            
        Returns:
            dict: 回复结果
        """
        url = 'https://qianchuan.jinritemai.com/ad/api/tool/v1/comment/batch_reply_comments'
        
        params = {
            'aavid': account,
            'gfversion': '1.0.1.7335'
        }
        
        data = {
            "commentIds": comment_ids,
            "replyText": reply_text,
            "requestType": 6,
            "aavid": account
        }
        
        headers = {
            'accept': 'application/json, text/plain, */*',
            'content-type': 'application/json',
            'cookie': self.cookies,
            'x-csrftoken': self.csrf_token,
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36'
        }
        
        response = self.session.post(url, headers=headers, params=params, json=data)
        return response.json()
    
    def 全域_修改计划(self, account: str,data):
        """修改计划
        
        Args:
            account (str): 账户ID
        """
        url = 'https://qianchuan.jinritemai.com/ad/api/creation/v1/ad/update'
        
        params = {
            'aavid': account,
            'gfversion': '1.0.0.4448',
        }
        
        headers = {
            'content-type': 'application/json',
            'cookie': self.cookies,
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36',
        }
        params['a_bogus'] = self.generate_a_bogus(json.dumps(params),json.dumps(data),headers['user-agent'])
        response = self.session.post(url, headers=headers, params=params, json=data)
        return response.json()
    
    
    def 全域_达人商品添加授权(self, account: str, aweme_uid: str, shop_id: str, mar_goal: int = 1):
        """全域达人商品添加授权
        Args:
            account (str): 账户ID
            aweme_uid (str): 达人抖音ID
            shop_id (str): 店铺ID
            mar_goal (int): 营销目标 默认1
        """
        url = 'https://qianchuan.jinritemai.com/ad/api/v1/account/uni_prom/apply-uni-prom-auth'
        
        params = {
            'aavid': account,
            'gfversion': '1.0.1.7216'
        }
        
        data = {
            'awemeUId': aweme_uid,
            'shopId': shop_id,
            'marGoal': mar_goal,
            'aavid': account
        }
        
        headers = {
            'content-type': 'application/json',
            'cookie': self.cookies
        }
        response = self.session.post(url, headers=headers, params=params, json=data)
        return response.json()
    
    def 全域_搜索达人(self, account: str, search_key: str, mar_goal: int = 2, page: int = 1, page_size: int = 100):
        """搜索达人
        
        Args:
            account (str): 账户ID
            search_key (str): 搜索关键词
            mar_goal (int): 营销目标 默认2
            page (int): 页码 默认1
            page_size (int): 每页数量 默认100
        """
        url = 'https://qianchuan.jinritemai.com/ad/api/creation/v1/aweme/get-aweme-list'
        
        params = {
            'page': page,
            'pageSize': page_size,
            'searchKeyWords': search_key,
            'awemeSourceMode': 'roi2',
            'needSingleVideoAuth': 0,
            'marGoal': mar_goal,
            'needProductUniPromApplyInfo': 1,
            'needProductRoi2Info': 1,
            'scene': 1,
            'authTypes': '1,2,3,5',
            'needApplyingAuth': 1,
            'needPagination': 1,
            'aavid': account,
            'gfversion': '1.0.1.7347'
        }
        
        headers = {
            'content-type': 'application/json',
            'cookie': self.cookies
        }

        retry_count = 0
        while retry_count < 3:
            response = self.session.get(url, headers=headers, params=params)
            if response.status_code == 504 and retry_count < 2:
                retry_count += 1
                time.sleep(1)  # 重试间隔1秒
                continue
            try:
                response = response.json()
                break
            except:
                raise Exception(response.text)
        try:
            return response['data']['userList']
        except:
            return []
    
    def 全域_推广计划_roi(self, account: str, ad_id: str, roi:str):
        """设置推广计划ROI
        
        Args:
            account (str): 账户ID
            ad_id (str): 推广计划ID
            roi (str): ROI值
        """
        url = 'https://qianchuan.jinritemai.com/ad/ecom/marketing/api/v1/promotion/update_uni_promotion_roi'
        params = {
            'aavid': account,
            'gfversion': '1.0.0.8914'
        }
        data ={
            "UpdateRoi2Infos": [
                {
                "value": roi,
                "ID": ad_id
                }
            ],
            "aavid": account
        }
        headers = {
            'content-type': 'application/json',
            'cookie': self.cookies,
            'x-csrftoken': self.csrf_token
        }
        
        response = self.session.post(url, headers=headers, params=params, json=data)
        try:
            return response.json()['data']['results'][0]['flag']
        except:
            return False

    def 全域_推广计划_启停(self, account: str, ad_id: str,enable:bool=True):
        """开启推广计划
        
        Args:
            account (str): 账户ID
            ad_id (str): 推广计划ID
            
        Returns:
            dict: 响应结果
        """
        url = 'https://qianchuan.jinritemai.com/ad/api/pmc/v1/batch_update_operation'
        
        params = {
            'aavid': account,
            'gfversion': '1.0.0.8914'
        }
        
        data = {
            "objects": [
                {
                    "objectID": ad_id,
                    "type": 1
                }
            ],
            "optType": 1 if enable else 2,
            "aavid": account
        }
        
        headers = {
            'content-type': 'application/json',
            'cookie': self.cookies,
            'x-csrftoken': self.csrf_token
        }
        
        response = self.session.post(url, headers=headers, params=params, json=data)
        try:
            return response.json()['data']['results'][0]['flag']
        except:
            return False
    

def main():
    ck=get_redis('抖音:巨量HHHhipapa2')
    api = Jlqc(ck)
    # rsp=api.account_data_quanyu2('****************',datetime(2025,1,14),datetime(2025,1,14,23,59,59),fittle_anchor_id='***************')
    # print(rsp)
    rsp=api.get_ad_hourly_data('****************',datetime(2025,5,29),datetime(2025,5,29,23,59,59))
    print(rsp)
    # start_date = datetime(2025, 2, 25)
    # end_date = datetime(2025, 2, 25,23,59,59)
    # result = api.get_material_crowd('****************','7463741058780823563',start_date,end_date,'user_group_label_name')
    # print(result)
    # name='沐言妹妹吖👑'
    # 删除name里面的表情 正则
    # name=re.sub(r'[\U00010000-\U0010FFFF]', '', name)
    # result=api.全域_推广计划列表('****************',start_date,end_date,name)
    # ad_list=[x['id'] for x in result['data']['adInfos'] if x['adDeliveryName']=='投放中' and x['name'].find(name)!=-1]
    # print(ad_list)
    # result=api.全域_获取计划素材('****************','****************')
    # result=api.全域_获取预估数据('****************','***********','3475309575866043680',5000,1,0)
    # result=api.全域_获取计划详情('****************','1818941014946908')
    # print(json.dumps(result['data']['adDetailInfo']['createMultiProductsCreative'][0]))
    # print(result['data']['adDetailInfo']['createMultiProductsCreative'][0])
    # print(len(result['data']['adDetailInfo']['createMultiProductsCreative']))
    # result = api.sucai_list_全域_直播间('****************',start_date,end_date,1,100,'7463741058780823563')
    # print(result)
    # search_key='831066434'
    # search_result=api.全域_搜索达人('1760224697894926','jiayou202066')
    # print(search_result)
    # exists=False
    # for item in search_result:
    #     if item['showId']!=search_key:
    #         continue
    #     exists=True
    #     aweme_uid=item['userId']
    #     showId = item['showId']
    # print(exists,aweme_uid,showId)
    # rsp=api.全域_达人商品添加授权('****************',aweme_uid,showId)
    # print(rsp)
    # print(api.全域_推广计划列表_optional('****************','b60f7fba29a1173e842f6d0f43a93189'))
    # date_str='2025-04-24'
    
    # print(api.获取评论回复列表('****************','7495360707492840227'))
    # print(api.获取评论列表('****************',date_str,date_str))
    # print(api.批量回复评论('****************',['7491563748816814848'],"亲亲小光盾pro防晒3岁以上，成人、敏肌、全肤质人群都可以使用呢~"))
    return
    # rsp=api.全域_创意_视频_同质化('****************','****************',datetime(2024,11,6),datetime(2024,11,6))
    data=api.account_data_biaozhu2('****************',datetime(2025,1,14),datetime(2025,1,14,23,59,59))
    data=data['Rows']
    data=filter(lambda x:x['Dimensions']['anchor_name']['Value']=='巧玲珑❤️',data)
    data=list(data)
    if not data:
        return None
    data=data[0]
    data=data['Rows']
    # print(data)
    rsp_list=[]
    date_str='2024-12-23'
    for item in data:
        hour=item['Dimensions']['stat_time_hour']['ValueStr'].split(' ')[1]
        rsp_list.append({
            '日期':date_str,
            '小时':hour,
            '消耗':item['Metrics']['stat_cost']['Value'],
            '成交金额':item['Metrics']['pay_order_amount']['Value'],
            '成交智能优惠券金额':item['Metrics']['pay_order_coupon_amount']['Value'],
            '7日总成交':item['Metrics']['all_order_pay_gmv_7days']['Value'],
        })
    print(rsp_list)



if __name__ == "__main__":
    main()
