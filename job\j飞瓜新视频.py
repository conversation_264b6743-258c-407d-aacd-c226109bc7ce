from datetime import datetime,timedelta
import json
import re
import sys
import time
sys.path.append('C:/Users/<USER>/Desktop/需求说明/API')

# from tool.Db import get_redis

from 飞书 import feishu_api
from 飞瓜 import Api
from 飞瓜D import Api as ApiD

class Job:
    book_id='ScGTssZP2huFwkta8KAcV6E7nCf'
    sheets={
        'main':'b6832f',
        '润本':'eydKhJ',
        '贝德美':'7mvBmI',
        '松达':'bm5dx0',
        '英氏':'7IAPLq',
        '小象妈咪':'SM9xtr',
        '袋鼠妈妈':'MfXtYb',
        '兔头妈妈':'NmYjZz',
    }
    
    def __init__(self) -> None :
        self.f_api=feishu_api('cli_a5d710c7cfbe500c',
                       'cvfoREoPFo6ifWOvPccUTcIu35oL0rVL')
        self.apiD=ApiD('13247302442', 'hipapa888')
        self.api=Api()
        # self.api.check_login()
    
    def sale_text_to_float(self,text):
        if text=='':
            return 0
        elif text[-1]=='w':
            return round(float(text[:-1])*10000,2)
        elif text.find('-'):
            return round(float(text.split('-')[0]),2)
        else:
            return round(float(text),2)
        
    def 处理字典数据(self,data):
        data['AwemeSaleGmvStr']=self.sale_text_to_float(data['AwemeSaleGmvStr'])
        return data
    
    def search_product_ui(self,title):
        # 接口加密了 改用ui搜索
        page=self.apiD.page
        page.get(f'https://dy.feigua.cn/app/#/promotion-search/index?keyword={title}')
        a=page('tag:a@@class=goods-title')
        if not a:
            return None
        url=a.attr('href')
        if not url:
            return None
        return url
    
    def run(self,date=None):
        f_api=self.f_api
        api=self.api
        rows=f_api.sheet_work_read(self.book_id,self.sheets['main'],'A:C')
        rows = [['' if column is None else column for column in row] for row in rows ]
        rows=[ [ column if isinstance(column, str)  else column[0]['text'] for column in row] for row in rows]
        add_sheet={}
        new_sheet={}
        add_count={}
        if date==None:
            date=datetime.now() - timedelta(days=1)
            today=date.strftime('%Y%m%d')
            yestoday=(date - timedelta(days=1)).strftime('%Y%m%d')
        else:
            date=datetime.strptime(date,'%Y%m%d')
            today=date.strftime('%Y%m%d')
            yestoday=(date - timedelta(days=1)).strftime('%Y%m%d')
        
        print(today,yestoday)
        
        for i,row in enumerate(rows[1:]):
            if row[2]:
                continue
            title=row[1]
            url=self.search_product_ui(title)
            if not url:
                print(f'{title} 未搜索到商品')
                f_api.sheet_work_write(self.book_id,self.sheets['main'],f'C{i+2}',[['未搜索到商品']])
                continue
            print(url)
            pattern = r'gid=(.*?)&'
            product_id = re.search(pattern, url).group(1)
            time.sleep(1)
            self.apiD.page.get(url)
            time.sleep(1)
            self.apiD.page('tag:div@@class=v-label@@text()=销售额',timeout=3)
            print(f'\r{product_id}',end='')
            today_list=api.product_video_list(product_id,today,pageSize=40)
            yestoday_list=api.product_video_list(product_id,yestoday,pageSize=40)
            # pyperclip.copy(json.dumps(yestoday_list,indent=4,ensure_ascii=False))
            yestoday_list['Data']['Items']=[self.处理字典数据(x) for x in yestoday_list['Data']['Items']]
            today_list['Data']['Items']=[self.处理字典数据(x) for x in today_list['Data']['Items']]
            
            yestoday_data={ video['UniqueId']:{'点赞':video['LikeCountStr'],'销售额':video['AwemeSaleGmvStr']} for video in yestoday_list['Data']['Items']}
            add_count[row[0]]=add_count.get(row[0],0)+1
            for video in today_list['Data']['Items']:
                # print(video)
                date_text=video['BaseAwemeDto']['AwemePubTime']
                if not date_text:
                    date_text=today
                else:
                    date_object = datetime.strptime(date_text, '%Y/%m/%d %H:%M:%S') #2024/08/26 17:01:18
                    date_text = date_object.strftime('%Y-%m-%d')
                tmp_list=add_sheet.get(row[0],[])
                环比销售额='' if yestoday_data.get(video["UniqueId"],None)==None else f'=({video["AwemeSaleGmvStr"]}-{yestoday_data[video["UniqueId"]]["销售额"]})/{yestoday_data[video["UniqueId"]]["销售额"]}'
                环比点赞='' if yestoday_data.get(video["UniqueId"],None)==None else f'=({video["LikeCountStr"]}-{yestoday_data[video["UniqueId"]]["点赞"]})/{yestoday_data[video["UniqueId"]]["点赞"]}'
                tmp_list.append([
                    today,
                    None,
                    video['BaseBloggerDto']['BloggerName'],
                    video['BaseAwemeDto']['AwemeShareUrl'],
                    date_text,
                    video['AwemeSaleGmvStr'],
                    video['AwemeSaleCountStr'],
                    '' if 环比销售额=='' else {'type':'formula','text':环比销售额},
                    '' if 环比点赞=='' else {'type':'formula','text':环比点赞},
                ])
                add_sheet[row[0]]=tmp_list
            # 获取新昨天新发布视频
            new_list=api.product_video_list(product_id,today,pageSize=50,only_new=True)
            tmp_list=new_sheet.get(row[0],[])
            # for video in new_list['data']['list']+last_list:
            for video in new_list['Data']['Items']:
                video['BaseBloggerDto']['Fans']=self.sale_text_to_float(video['BaseBloggerDto']['Fans'])
                tmp_list.append({
                    'aweme_title':video['BaseAwemeDto']['AwemeDesc'],
                    'nickname':video['BaseBloggerDto']['BloggerName'],
                    'aweme_url':video['BaseAwemeDto']['AwemeShareUrl'],
                    'date':today,
                    'follower_count':video['BaseBloggerDto']['Fans'],
                    'label': '星图' if video['BaseBloggerDto']['Fans']>=500000 else '抖客'
                })
            new_sheet[row[0]]=tmp_list
            
            # break
        # print(json.dumps(add_sheet,indent=4,ensure_ascii=False))
        # win剪切板获取文本
        # pyperclip.copy(json.dumps(add_sheet,indent=4,ensure_ascii=False))
        for key in add_sheet:
            # 清空原有数据
            f_api.sheet_work_write(self.book_id,self.sheets[key],'A2',
            add_sheet[key]+[[None]*9]*(add_count[key]*20-len(add_sheet[key]))
        )
        # print(new_sheet)
        # 插入新添加视频 #竞品短视频数据登记-（蝉妈妈）- 每日新增视频    
        append_data=[]
        for k in new_sheet:
            for item in new_sheet[k]:
                append_data.append([k,item['date'],item['nickname'],item['aweme_title'],item['aweme_url'],item['follower_count'],item['label']])
        # print(append_data)
        f_api.sheet_work_append(self.book_id,'QsGBc9','A:A',append_data)
            
            

def main():
    j=Job()
    j.run()
    # j.apiD.chrome_page.quit()
    j.apiD.page.close()
    # j.run('2024-07-03')

if __name__ == "__main__":
    main()