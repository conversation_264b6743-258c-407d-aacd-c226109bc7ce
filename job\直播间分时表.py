import datetime
import sys
import os
import time
from typing import Any

from DrissionPage import ChromiumPage, ChromiumOptions
import pyperclip
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

import re
from 飞书 import feishu_api
from 巨量千川 import Jlqc
from 罗盘 import Api as lp_api  # 用于获取直播间数据
from 罗盘_达人 import Api
from tool.Db import get_redis
from tool.Fn import Table,datetime_to_excel_int,convert_to_datetime,get_clipboard_image
# from 巨量千川 import Api
from job.巨量分时数据 import Job as Job_分时



class Job:
    co = None
   
    code_map_live_room = None
    对照 = {
        '品牌号': '海龟爸爸Hi!papa',
        '洁面A': '青少年',
        '官号': 'hipapa',
        '母婴号': '海龟爸爸',
        '气垫号': '爆量王',
        # 'UV相机（小光盾）': 'uv相机',
    }
    对照books = {
        '官号': {'id': 'OyCQsuwYAhmRWjtD8uschVg9nmh', 'sheet': '4miRhV', 'copy_id': 'PaJV7L','copy_url':'https://haiguibaba.feishu.cn/sheets/OyCQsuwYAhmRWjtD8uschVg9nmh?sheet=PaJV7L&rangeId=PaJV7L_6vwkK7d95y&rangeVer=1'},
        '洁面A': {'id': 'QvLhsc4wwhfniQty4nIckX2hnNb', 'sheet': '4miRhV', 'copy_id': 'kdHGG4','copy_url':"https://haiguibaba.feishu.cn/sheets/QvLhsc4wwhfniQty4nIckX2hnNb?sheet=kdHGG4&rangeId=kdHGG4_iiqnwHy4RI&rangeVer=1"},
        '母婴号': {'id': 'QuhHsj7qqhcqW4tkV9rcdSemnBh', 'sheet': '4miRhV', 'copy_id': 'QX96uH','copy_url':'https://haiguibaba.feishu.cn/sheets/QuhHsj7qqhcqW4tkV9rcdSemnBh?sheet=QX96uH&rangeId=QX96uH_rLOWoBfHBZ&rangeVer=1'},
        '品牌号': {'id': 'RJw7slh53hxHtttv1a0cDHRFnte', 'sheet': '4miRhV', 'copy_id': 'BriNrn','copy_url':"https://haiguibaba.feishu.cn/sheets/RJw7slh53hxHtttv1a0cDHRFnte?sheet=BriNrn&rangeId=BriNrn_7N3xxk9iQV&rangeVer=1"},
        '气垫号': {'id': 'LWpdso9xnhaOD1tnAOtcqCapnKc', 'sheet': '4miRhV', 'copy_id': 'UojQva','copy_url':"https://haiguibaba.feishu.cn/sheets/LWpdso9xnhaOD1tnAOtcqCapnKc?sheet=UojQva&rangeId=UojQva_XYzRAgB3Pk&rangeVer=1"},
        # 'UV相机（小光盾）': {'id': 'ZwUPszbQvhW0Hdt2Y6ycs0R0nag', 'sheet': 'ee9161', 'copy_id': 'jcL9RQ','copy_url':"https://haiguibaba.feishu.cn/sheets/ZwUPszbQvhW0Hdt2Y6ycs0R0nag?sheet=jcL9RQ&rangeId=jcL9RQ_FJPMFsiAai&rangeVer=1"},
    }
    account_data = {}

    def __init__(self) -> None:
        self.fapi = feishu_api()
        ck = get_redis('抖音:罗盘-主-5311')
        self.lp_api = lp_api(ck)
        self.rd = get_redis()
        self.job=Job_分时()
        self.api = Jlqc(get_redis('抖音:巨量HHHhipapa2'))

    def 获取对应分组户id(self):
        """
        Returns:
            dict: 包含账户与对应的分组户id的字典。
        """
        # https://haiguibaba.feishu.cn/sheets/JeRoshPE4hnucPtABilcVnAvn36?sheet=Z94RCk
        api = self.fapi
        # 总表
        # https://haiguibaba.feishu.cn/sheets/JeRoshPE4hnucPtABilcVnAvn36?sheet=Z94RCk
        rows = api.sheet_work_read(
            "JeRoshPE4hnucPtABilcVnAvn36", 'Z94RCk', "A:B", 'ToString')
        # print(rows)
        account = None
        data = {}
        for row in rows[::-1]:
            text = row[0]
            # print(text)
            if isinstance(text, list):
                text = [x['text'] for x in text if 'text' in x][0]
            # 过滤账户的特殊字符: '-'
            if isinstance(text, str):
                text = text.strip().replace('-', '')
            elif isinstance(text, int):
                text = str(text)
            # 该逻辑是判断是否为账户行
            if len(row) > 1 and row[1] == '投放ROI':
                account = row[0]
                # print(account)
            # 该逻辑是判断是否为分组户id
            elif not text:
                continue
            elif account and len(row) > 0 and text[:2] == '全域' and re.match(r'^\d+$', text[2:]):
                data.setdefault(account, [])
                data[account].append(row[0])
            elif account and len(row) > 0 and re.match(r'^\d+$', text):
                # print(row[0])
                data.setdefault(account, [])
                data[account].append(row[0])
            # 该逻辑是判断是否为下一账户
            elif account:
                account = None
        # data['品牌号']=['全域****************'] # 布丁说固定的
        # 去重
        for k in data:
            data[k] = list(set(data[k]))
        self.account_data = data
        return data


    def get_live_room_code(self):
        """
        获取直播间对照关系
        https://haiguibaba.feishu.cn/sheets/CPDSsQPvChVUVGtQuXWcWTnjnqh?sheet=8f8f51
        """
        if self.code_map_live_room:
            return self.code_map_live_room
        rsp = self.fapi.sheet_work_read(
            'CPDSsQPvChVUVGtQuXWcWTnjnqh', '8f8f51', 'A:C')
        name2id = self.rd.hgetall('job:巨量开发者_直播间id2Name')
        name2id = {y.decode(): x.decode() for x, y in name2id.items()}
        code_map_live_room = {item[2]: name2id.get(
            item[0], None) for item in rsp}
        map_live_room={item[2]: item[0] for item in rsp}
        self.code_map_live_room = code_map_live_room
        self.map_live_room=map_live_room
        return code_map_live_room

    def get_table_data(self,sheet_id:str,book_id:str):
        """
        获取飞书表格数据
        """
        rows=self.fapi.sheet_work_read(sheet_id,book_id,'A:R')
        # 获取最下面一个日期
        tabel = None
        for i,row in enumerate(rows):
            if row[0] and isinstance(row[0],str) and row[0].find('日期')!=-1:
                tabel=rows[i:i+21]

        if not tabel:
            raise Exception('没有找到表格')
        return tabel
    
    def add_new_data(self,table:list[list[str]],book_id:str,sheet_id:str):
        """
        添加新一天数据表格
        """
        # 找到并截断表格中的日期行
        # for i, row in enumerate(table):
        #     if row[0] and isinstance(row[0],str) and row[0].find('日期')!=-1:
        #         table=table[i:]
        #         break

        # 打印表格中第二行的第9个元素
        # print(table[1][8])
        # 获取当前日期并格式化
        today=datetime.datetime.today()
        today=today.replace(hour=0,minute=0,second=0,microsecond=0)
        today=datetime_to_excel_int(today)

        # 从表格中提取开始行和开始日期
        start_row = int (re.search(r'\d+',table[1][8]).group())
        start_date = table[1][0]
        if start_date==today:
            print('不用添加新表')
            return False

        # 初始化新表格和手动数据列表
        new_table=[]
        manual_data=[]

        # 提取手动填写的数据列索引
        for key in ['累计GMV','累计成交人数','直播场观','投放成交','投放消耗','主播','中控']:
            manual_data.append(table[0].index(key))
        # today_int=datetime_to_excel_int(today)
        # 遍历表格，处理每一行数据
        for row in table:
            # 替换日期
            if isinstance(row[0],int) and row[0]==start_date:
                row[0]=today
            elif isinstance(row[0],int): # 日期不等于start_date
                row[0]=today+1

            # 遍历行中的每个元素
            for i,item in enumerate(row):
                # 只处理公式
                if not isinstance(item,str) or not item.startswith('='):
                    continue
                
                # 使用正则表达式匹配公式中的所有行号，并按从大到小排序后依次替换
                matches = re.findall(r'[A-Z](\d+)', item)
                matches = sorted(set(matches), key=lambda x: int(x), reverse=True)
                formula_text = item
                for match in matches:
                    old_row = match
                    new_row = f'{int(match)+len(table)}'
                    formula_text = formula_text.replace(old_row, new_row)
                row[i] = {
                    "type": "formula",
                    "text": formula_text
                }

            # 处理几个手动填的数
            if isinstance(row[0],float):
                for i in manual_data:
                    row[i]=''
            # 将处理后的行添加到新表格中
            # 最后插入日期
            if isinstance(row[0],str) and row[0].find('日期')!=-1:
                pass
            else:
                row[-1]=today
            new_table.append(row)
        table_len = len(new_table)-1
        # 调用API将新表格添加到工作表中
        rsp=self.fapi.sheet_work_write(book_id,sheet_id,f"A{start_row+table_len}",new_table)
        if rsp['msg']!='success':
            raise Exception(rsp['msg'])
        time.sleep(3)
        return True
        
    def get_live_room_data(self, author_name:str ,date:datetime.datetime):
        """
        获取直播间数据

        Args:
            author_name (str): 主播名称
            date (datetime.datetime): 日期

        Returns:
            dict: 包含直播间数据的字典 {小时: {'成交金额': 0, '累计金额': 0, '进入人数': 0, '成交人数': 0}}

        """
        # 获取直播间代码映射
        get_live_room_code = self.get_live_room_code()
        # {'官号': '94788331397', '洁面A': '1953175320664781', '品牌号': '62898740116', '母婴号': '2515293289855342', '气垫号': '3164861191694435', 'UV相机（小光盾）': '61922561316'}
        # 调用直播列表接口获取直播间数据
        room_code = get_live_room_code[author_name]
        if not room_code:
            print(f'搜索直播间author_id{self.map_live_room[author_name]}')
            print(self.map_live_room)
            rsp=self.lp_api.search_live_room(self.map_live_room[author_name])
            room_code=rsp['data'][0]['author_id']
            
        print(room_code)
        rsp = self.lp_api.live_list_room(room_code, date=date)
        try:
            # 提取直播间ID列表
            room_ids = [x['live_room_id'] for x in rsp['data']['card_list']]
        except Exception as e:
            print(f'直播间2 {id} 没有数据 {date.strftime("%Y-%m-%d")}')
            return

        # 初始化存储直播间数据的列表
        datas_成交金额 = []
        datas_进入人数 = []
        datas_成交人数 = []
        # datas_分时成交 = []

        # 遍历直播间ID列表，获取各个直播间数据
        for room_id in room_ids:
            print('直播间id',room_id)
            rsp = self.lp_api.live_room_shop(room_id, 'watch_ucnt,pay_amt')
            # 提取成交金额数据
            items = [x for x in rsp['data']['trends']
                     if x['display_name'] == '成交金额']
            datas_成交金额 += items
            # 提取进入人数数据
            items1 = [x for x in rsp['data']['trends']
                      if x['display_name'] == '进入人数']
            datas_进入人数 += items1
            rsp = self.lp_api.live_room_shop(
                room_id, 'pay_ucnt,gpm')
            # 提取成交人数数据
            items2 = [x for x in rsp['data']['trends']
                      if x['display_name'] == '成交人数']
            # items3: list[Any] = [x for x in rsp['data']['trends']
            #                      if x['display_name'] == '成交人数']
            datas_成交人数 += items2
            # datas_分时成交 += items3

        # 设置起始时间为当天早上6点
        start_date = date.replace(hour=6)
        hour_datas = {}
        now = datetime.datetime.now()
        now = now.replace(minute=0,second=0,microsecond=0)
        
        # 遍历24小时，计算每小时的直播间数据,日不落会+多一天
        for i in range(20): # 20+24
            end_date = start_date + datetime.timedelta(seconds=3599)
            mix = 0
            mix_top = 0
            mix_top_成交人数 = 0
            hour = i+6
            if start_date >=now:
                break
            # 如果小时为0，则设置为24
            # if hour == 0:
            #     hour = 24
            # # 如果小时为1，则设置为25
            # elif hour == 1:
            #     hour = 25
            hour_datas.setdefault(hour, {})
            # 计算成交金额
            exists_data=False
            for rows in datas_成交金额:
                date_time = datetime.datetime.fromtimestamp(rows['date_time'])
                if date_time >= start_date and date_time <= end_date:
                    exists_data =True
                    mix += rows['vertical'] / 100
                if date_time <= end_date:
                    exists_data =True
                    mix_top += rows['vertical'] / 100
            if not exists_data:
                start_date += datetime.timedelta(hours=1)
                continue
            hour_datas[hour]['成交金额'] = round(mix, 2)
            hour_datas[hour]['累计金额'] = round(mix_top, 2)
            mix = 0
            # 计算进入人数
            for rows in datas_进入人数:
                date_time = datetime.datetime.fromtimestamp(rows['date_time'])
                if date_time >= start_date and date_time <= end_date:
                    mix += rows['vertical']
            hour_datas[hour]['进入人数'] = round(mix, 2)
            
            mix = 0
            # 计算成交人数
            for rows in datas_成交人数:
                date_time = datetime.datetime.fromtimestamp(rows['date_time'])
                if date_time >= start_date and date_time <= end_date:
                    mix += rows['vertical']
                if date_time <= end_date:
                    mix_top_成交人数 += rows['vertical']
                    
            hour_datas[hour]['成交人数'] = round(mix, 2)
            hour_datas[hour]['累计成交人数'] = round(mix_top_成交人数, 2)
            
            
            start_date += datetime.timedelta(hours=1)
        # 打印并返回小时级直播间数据
        # print(hour_datas)
        return hour_datas

    def run(self):
        self.获取对应分组户id()
        for k in self.对照:
            print(f'正在处理账号 {k}')
            self.do_account(k)
        self.chrome_page.quit()
        
    
    
    def do_account(self,account_name):
        # print(self.account_data)
        # date = datetime.datetime.now()
        # date = date.replace(hour=0, minute=0, second=0, microsecond=0)
        # date= date-datetime.timedelta(days=1)
        # rsp=self.get_live_room_data('气垫号',date)
        # print(rsp)
        
        # date = date-datetime.timedelta(days=1)
        # print(len(items1))
        # print(len(items2))
        book_id=self.对照books[account_name]['id']
        sheet_id=self.对照books[account_name]['sheet']
        today=datetime.datetime.now()
        today=today.replace(hour=0,minute=0,second=0,microsecond=0)
        for _ in range(2):
            # print('进入1')
            rsp=self.get_table_data(book_id,sheet_id)
            table=Table(rsp)
            # print(len(table))
            need_edit=False
            empt_table = [row for row in table if not row['累计GMV']]
            # 取第一条没执行的日期,看是否大于当前时间 是不用处理
            if empt_table and convert_to_datetime(empt_table[0][0]) > datetime.datetime.now():
                print('不需要处理,大于当前时间')
                break  
            if empt_table:
                # 需要填数据
                start_row = int (re.search(r'\d+',table[1][8]).group())
                start_date=table[1][0]
                start_date=convert_to_datetime(start_date)
                start_date=start_date.replace(hour=0,minute=0,second=0,microsecond=0)
                # 获取直播数据
                live_room_data=self.get_live_room_data(account_name,start_date)
                print('live_room_data',live_room_data)
                
                hour=6 # 从6点开始
                append_data,start_edit_row=[],-1
                
                # 找到第一个没有数据的时间段
                first_empty_hour = None
                for i in range(len(table)):
                    if not table[i]['累计GMV']:
                        first_empty_hour = hour + i
                        break
                    
                print(f'第一个没有数据的时间段: {first_empty_hour}')
                

                
                # 获取投放户数据，只获取从first_empty_hour开始的时间段数据
                hours_data=self.get_account_list_分时_消耗成交(account_name, start_date, start_hour=first_empty_hour)
                print('hours_data',hours_data)
                
                # 如果start_date是今天 则不需要24以及之后的
                if start_date>=today:
                    
                    # 判断后再删除hours_data中的24和25键
                    if 24 in hours_data:
                        del hours_data[24]
                    if 25 in hours_data:
                        del hours_data[25]
                for i in range(len(table)):
                    # 如果数据已经存在 则跳过
                    if table[i]['累计GMV'] and not need_edit:
                        hour+=1
                        continue
                    if hour not in hours_data and hour not in live_room_data:
                        hour+=1
                        break
                    if not need_edit:
                        start_edit_row = start_row + i - 1 
                        need_edit = True
                    # 如果数据不存在 则填数据 累计GMV	累计成交人数 直播场观	投放成交	投放消耗
                    table[i]['累计GMV']=live_room_data.get(hour,{}).get('累计金额',0)
                    # table[i]['累计成交人数']=live_room_data.get(hour,{}).get('累计成交人数',0)
                    # table[i]['直播场观']=live_room_data.get(hour,{}).get('进入人数',0)
                    # table[i]['直播场观']=0 # TODO
                    table[i]['投放消耗']=hours_data.get(hour,{}).get('消耗',0)
                    table[i]['投放成交']=hours_data.get(hour,{}).get('成交金额',0)
                    append_data.append(table[i])
                    hour+=1
            # 判断是否已经全部填完 没有就直接退出 TODO
            if need_edit: 
                # 生成插入数据
                insert_data = [[item['累计GMV']] for item in append_data] # ,item['累计成交人数']
                insert_data2 = [[item['投放成交'],item['投放消耗']] for item in append_data] # item['直播场观']因为这个没有分时数 有人工手动填
                ok = self.fapi.sheet_work_write(book_id,sheet_id,f"G{start_edit_row}:G{start_edit_row+len(insert_data)-1}",insert_data)
                if ok['code']!=0:
                    print(ok)
                    raise Exception('插入失败')
                ok = self.fapi.sheet_work_write(book_id,sheet_id,f"L{start_edit_row}:M{start_edit_row+len(insert_data2)-1}",insert_data2)
                if ok['code']!=0:
                    print(ok)
                    raise Exception('插入失败')
            else:
                print('不需要编辑')
            last_data=table[-1]['累计GMV']
            # print(last_data)
            # 如果还有待填报不需要添加
            if last_data is None or isinstance(last_data,str) and last_data=='':
                print('不需要明天')
                break
            # 判断是否最新一天不是插入 然后继续填数
            add_ok =  self.add_new_data(rsp,book_id,sheet_id)
            if not add_ok:
                break
            
        # 截图
        sheet_id = self.对照books[account_name]['copy_id']
        copy_url = self.对照books[account_name]['copy_url']
        today_int = datetime_to_excel_int(start_date)
        self.fapi.sheet_work_write(book_id,sheet_id,'B1',[[today_int]])
        time.sleep(1)
        self.send_feishu_screenshot(copy_url)

    def send_feishu_screenshot(self,copy_url:str,web_url:str=None):
        if not web_url:
            web_url='https://open.feishu.cn/open-apis/bot/v2/hook/36592a5f-6120-4121-b165-02c32fdc03a1'
        if not self.co:
            self.co = ChromiumOptions().set_local_port(9112).set_user_data_path(r'.\user-data')
            self.chrome_page = ChromiumPage(self.co)
        page: ChromiumPage | Any=self.chrome_page
        # 清空系统剪切板
        pyperclip.copy('')
        page.get(copy_url)
        # 右键菜单
        page('#miniapp-faster').click.at(185,70,'right')
        time.sleep(13)
        # 复制为图片
        page('tag:div@@class:content-wrapper@@text():复制为图片').click()
        time.sleep(1)
        # 获取剪贴板
        return_bytes=get_clipboard_image()
        if return_bytes:
            image_key=self.fapi.upload_image(return_bytes)
            print(image_key)
            data={
                "msg_type":"image",
                "content":{
                    "image_key": image_key
                }
            }
            
            rsp=self.fapi.send_webhook(web_url,data)
            if not rsp:
                raise Exception(rsp)
    

    def get_account_list_分时_消耗成交(self,account_name:str,date:datetime.datetime,get_next_hours:bool=False,start_hour:int=None):
        """
        获取投放户数据
        
        Args:
            account_name (str): 账户名称
            date (datetime.datetime): 日期
            get_next_hours (bool): 是否获取次日0-1点数据
            start_hour (int): 开始获取数据的小时，如果设置，则只获取该小时及之后的数据
            
        Returns:
            dict: 小时级投放数据 {小时: {'消耗': 0, '成交金额': 0}}
        """
        today = datetime.datetime.now()
        # today = today.replace(hour=0,minute=0,second=0,microsecond=0)
        
        if not self.account_data:
            self.获取对应分组户id()
        # 获取投放户
        account_list=self.account_data[account_name]
        # print(account_list)
        # print('-'*30)
        # 投放户类型
        account_list_type=self.job.get_date_account(date)
        account_list_type_n={}
        for k in account_list_type:
            for a in account_list_type[k]:
                account_list_type_n[f'{a}']=k
        account_list_type = account_list_type_n
        del account_list_type_n
        # print(account_list_type)
        hours_data={}
        for account in account_list:
            account_type=account_list_type.get(account)
            if not account_type:
                print(f'{account} 没有投放类型')
                continue
            # print(f'{account} {account_type}')
            # 获取投放户数据
            if account_type.find('全域')!=-1:
                print(f'全域{account}')
                rsp=self.api.get_ad_hourly_data(account,date,date+datetime.timedelta(hours=24))
                # 处理rsp
                new_rsp=[]
                today_tmp = today.replace(minute=0,second=0,microsecond=0)
                
                for item in rsp['data']['StatsData']['Rows']:
                    stat_time_hour = item['Dimensions']['stat_time_hour']['ValueStr']
                    stat_time_hour = datetime.datetime.strptime(stat_time_hour, '%Y-%m-%d %H:%M')
                    print(stat_time_hour,today_tmp.day == stat_time_hour.day , today_tmp.hour <= stat_time_hour.hour)
                    if today_tmp.day == stat_time_hour.day and today_tmp.hour <= stat_time_hour.hour:
                        continue
                    new_rsp.append({
                        '小时':stat_time_hour.hour,
                        '消耗':item['Metrics']['stat_cost']['Value'],
                        '成交金额':item['Metrics']['total_pay_order_gmv_for_roi2']['Value']
                    })
                rsp=new_rsp
            elif account_type=='直播间':
                rsp=self.job.get_qianchuan_account_标准_直播间(date,account,not_check_fn=True,type_name_fn='直播分时')
            elif account_type=='商品':
                rsp=self.job.get_qianchuan_account_标准_商品(date,account,not_check_fn=True,type_name_fn='直播分时')
            else:
                print(f'{account} 没有投放类型')
                continue
            if not rsp:
                print(f'{account} 没有数据')
                continue
            for item in rsp:
                if isinstance(item['小时'],int):
                    hour=item['小时']
                else:
                    hour=int(item['小时'].split(':')[0])
                if hour<2: #and account_type.find('直播')!=-1
                    hour+=24
                if '消耗' not in item:
                    item['消耗']=item.get('直投消耗',0)+item.get('素材消耗',0)
                if '成交金额' not in item:
                    item['成交金额']=item.get('直投成交金额',0)+item.get('素材成交金额',0) #+item.get('创意成交',0)+item.get('成交智能优惠券金额',0)
                hours_data.setdefault(hour,{'消耗':0,'成交金额':0})
                hours_data[hour]['消耗']+=item['消耗']
                hours_data[hour]['成交金额']+=item['成交金额']
        
        today_0= today.replace(hour=0,minute=0,second=0,microsecond=0)
        if not get_next_hours and date<today_0:
            if 24 in hours_data:
                del hours_data[24]
            if 25 in hours_data:
                del hours_data[25]
            # 如果今天和投放日期相差不到1小时 则返回
            # print(today-date<datetime.timedelta(hours=25))
            # print((today-date).total_seconds()/60/60)
            if today-date<datetime.timedelta(hours=25):
                return hours_data
            next_hours_data=self.get_account_list_分时_消耗成交(account_name,date+datetime.timedelta(days=1),get_next_hours=True)
            print('next_hours_data',next_hours_data)
            # 只要24以及之后的
            # print('进入2')
            if today-date>datetime.timedelta(hours=25):
                hours=[24]
            elif today-date>datetime.timedelta(hours=26):
                hours=[24,25]
            for hour in hours:
                if hour not in next_hours_data:
                    continue
                hours_data.setdefault(hour,{})
                hours_data[hour]['消耗']=next_hours_data[hour].get('消耗',0)
                hours_data[hour]['成交金额']=next_hours_data[hour].get('成交金额',0)
        return hours_data
                
            
    def test(self):
        date=datetime.datetime.now()
        date=date.replace(hour=0,minute=0,second=0,microsecond=0)
        # date=date-datetime.timedelta(days=1)
        # rsp=self.get_account_list_分时_消耗成交('母婴号',date)
        
        # self.send_feishu_screenshot('https://haiguibaba.feishu.cn/sheets/QuhHsj7qqhcqW4tkV9rcdSemnBh?sheet=QX96uH&rangeId=QX96uH_rLOWoBfHBZ&rangeVer=1')
        # 母婴号 测试添加新表
        # 获取母婴号的book_id和sheet_id
        name='气垫号'
        book_id = self.对照books[name]['id']
        sheet_id = self.对照books[name]['sheet']
        # 获取表格数据
        # table = self.get_table_data(book_id, sheet_id)
        # live_room_data=self.get_account_list_分时_消耗成交(name,datetime.datetime(2025,5,28),get_next_hours=False) # 2025-05-26 00:00:00
        # 调用添加新一天数据表格的方法
        # result = self.add_new_data(table, book_id, sheet_id)
        # print(f"添加新表结果: {live_room_data}")
        # rsp=self.get_live_room_data(name,date)
        # print(rsp)
        
        
if __name__ == '__main__':
    job = Job()
    
    job.run()
