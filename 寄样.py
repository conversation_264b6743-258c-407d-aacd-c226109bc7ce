from web_api import WebApi
from 飞书 import feishu_api,get_people
import traceback

import datetime

f_api = feishu_api('cli_a5d710c7cfbe500c',
                   'cvfoREoPFo6ifWOvPccUTcIu35oL0rVL')
api = WebApi('majianping', 'op1314520.')


def get_djbh():
    rsp = f_api.sheet_work_read(
        'A5bostiJMhnI8WtiopmcXHCWnob',
        'nabLSb',
        'A1',
    )
    rsp = rsp[0][0]
    if rsp[4:12] != datetime.date.today().strftime('%Y%m%d'):
        djbh = f"QTCK{datetime.date.today().strftime('%Y%m%d')}0001"
    else:
        djbh = rsp[:-4]+'{:04d}'.format(int(rsp[-4:])+1)
    rsp = f_api.sheet_work_write(
        'A5bostiJMhnI8WtiopmcXHCWnob',
        'nabLSb',
        'A1',
        [[djbh]]
    )
    return djbh


def main():

    # api = Baisheng('http://*************:30003/webopm/web/',
    #                'hipapa', 'Y5Zxb2TGa5tkaZSZyJY')
    goods_code = api.get_goods()
    need_shipping = []
    start_row = 2
    # row = 547
    rows = f_api.sheet_work_read(
        'A5bostiJMhnI8WtiopmcXHCWnob',
        '8ce518',
        f'A:R',
    )
    # rows+=[  
    # ['李益','抖音仓','线上分销部','分销','刘小姐','13712939093','广东省东莞市虎门镇富民商业大厦二楼西区2611', '中通（线上）','6974560290932:5','',None,None,None,None,None,None,None]
    # ]
    # for row in rows[-4:]:
    #     print(row)
    # exit()
    for row, row_data in enumerate(rows[start_row-1:]):
        row += start_row
        print(f'\r{row}', end='')
        if not row_data:
            continue
        # 如果 商品SKU1 为空退出
        事由=row_data[ord("L")-65]
        if not row_data[ord("J")-65]:
            continue
        
        # 如果状态有数据跳过
        if row_data[ord("N")-65]:
            # 如果单据号没 就后面来查询
            if not row_data[ord("P")-65]:
                need_shipping.append(
                    {'id': row_data[ord("O")-65], 'cell': f'P{row}'})
            continue
        elif  事由==None or not isinstance(事由,str) or 事由.strip() == '':
            f_api.sheet_work_write(
                    'A5bostiJMhnI8WtiopmcXHCWnob',
                    '8ce518',
                    f'N{row}',
                    [[f'失败:事由为必填项目']]
                )
            continue
        商店=row_data[ord("E")-65]
        if not 商店:
            f_api.sheet_work_write(
                    'A5bostiJMhnI8WtiopmcXHCWnob',
                    '8ce518',
                    f'N{row}',
                    [[f'失败:商店为必填项目']]
                )
            continue
        # 快递处理
        if not row_data[ord("I")-65]:
            row_data[ord("I")-65] = '中通（线上线下通用）'
        # elif row_data[ord("I")-65] not in ['中通（线上）', '邮政', '顺丰']:
        #     f_api.sheet_work_write(
        #         'A5bostiJMhnI8WtiopmcXHCWnob',
        #         '8ce518',
        #         f'M{row}',
        #         [[f'失败: 快递输入有误']]
        #     )
        #     continue
        # '
        error_data={}
        # 创建订单
        #   获取单据号
        try:
            # print(f'\r{row}/1',end='')
            djbh = get_djbh()
            date = datetime.date.today().strftime('%Y-%m-%d')
            # 备注
            登记人=row_data[ord('A')-65]
            登记人=登记人 if 登记人 else ''
            事由=row_data[ord("L")-65]
            事由=事由 if 事由 else ''
            是否新品=row_data[ord("K")-65]
            是否新品=是否新品 if 是否新品 else ''
            备注=f'{登记人} {是否新品} {事由}'
            rsp = api.stock_crtzd_rk_add(
                djbh, date, row_data[ord('B')-65], row_data[ord('C')-65],商店, row_data[ord('D')-65], 备注)
            if not rsp['ok']:
                print(row, '创建失败1')
                f_api.sheet_work_write(
                    'A5bostiJMhnI8WtiopmcXHCWnob',
                    '8ce518',
                    f'N{row}',
                    [[f'失败:{rsp["msg"]}']]
                )
                continue
            id = rsp['id']
            #   更新订单号
            f_api.sheet_work_write(
                'A5bostiJMhnI8WtiopmcXHCWnob',
                '8ce518',
                f'M{row}',
                [[djbh]]
            )
            f_api.sheet_work_write(
                'A5bostiJMhnI8WtiopmcXHCWnob',
                '8ce518',
                f'O{row}',
                [[str(id)]]
            )
            # 更新收件人信息
            # print(f'\r{row}/3',end='')
            stock_result = api.stock_crtzd_rk_ex(
                id, row_data[ord("F")-65], row_data[ord("G")-65], row_data[ord("H")-65], row_data[ord("I")-65])
            # if not ok:
            #     print(row, '创建失败2')
            #     f_api.sheet_work_write(
            #         'A5bostiJMhnI8WtiopmcXHCWnob',
            #         '8ce518',
            #         f'N{row}',
            #         [[f'失败:更新联系人失败']]
            #     )
            #     continue
            # 添加商品
            goods = str(row_data[ord("J")-65])
            goods = goods.replace('，', ',').replace(
                '：', ':').replace('；', ',').replace(';', ',')
            goods = goods.split(',')
            goods = [{'sku': x.split(':')[0].strip(), 'sl': x.split(':')[1].strip(), 'id': goods_code[x.split(':')[0].strip()]}
                     if x.count(':') > 0
                     else
                     {'sku': x.strip(), 'sl': 1, 'id': goods_code[x]}
                     for x in goods if x.strip() != '']
            # print(f'\r{row}/4',end='')
            ok = api.stock_crtzd_rk_add_goods(id, goods)
            if not ok:
                print(row, '创建失败3')
                f_api.sheet_work_write(
                    'A5bostiJMhnI8WtiopmcXHCWnob',
                    '8ce518',
                    f'N{row}',
                    [[f'失败:添加商品失败']]
                )
                continue
            if stock_result == '操作成功':
                f_api.sheet_work_write(
                    'A5bostiJMhnI8WtiopmcXHCWnob',
                    '8ce518',
                    f'N{row}',
                    [[f'完成']]
                )
            else:
                f_api.sheet_work_write(
                    'A5bostiJMhnI8WtiopmcXHCWnob',
                    '8ce518',
                    f'N{row}',
                    [[f'失败:订单创建 但是地址解析异常(请手动到E3手工补充地址)']]
                )
            f_api.sheet_work_write(
                'A5bostiJMhnI8WtiopmcXHCWnob',
                '8ce518',
                f'Q{row}',
                [[datetime.datetime.now().strftime(r'%Y/%m/%d %H:%M')]]
            )
        except Exception as e:
            print(traceback.print_exc())
            f_api.sheet_work_write(
                'A5bostiJMhnI8WtiopmcXHCWnob',
                '8ce518',
                f'N{row}',
                [[f'失败:{traceback.format_exc()}']]
            )
    print('\r开始查询单号', end='')
    # 查询物流单号
    for item in need_shipping:
        if not item['id']:
            continue
        rsp = api.stock_get_crtzd_ex(item['id'])
        if rsp['shipping_sn']:
            f_api.sheet_work_write(
                'A5bostiJMhnI8WtiopmcXHCWnob',
                '8ce518',
                item['cell'],
                [[rsp['shipping_sn']]]
            )
    
def send_error():
    rows = f_api.sheet_work_read(
        'A5bostiJMhnI8WtiopmcXHCWnob',
        '8ce518',
        f'A:T',
    )
    
    start_row = 2
    peoples=get_people()
    error_data={} # name:[{i,order,type}]
    for row, row_data in enumerate(rows[start_row-1:]):
        row+=start_row
        # print(row)
        name=row_data[ord('A')-65]
        if not name:
            continue
        elif row_data[ord("R")-65]:
            continue
        elif row_data[ord("N")-65]!='完成' and row_data[ord("M")-65]:
            item_error=error_data.get(name,[])
            item_error.append({
                'i':row,
                'order':row_data[ord("M")-65],
                'type':'完成失败'
            })
            error_data[name]=item_error
        elif not row_data[ord("P")-65] and row_data[ord("Q")-65]:
            tmp=row_data[ord("Q")-65]
            if not isinstance(tmp,str):
                continue
            table_time=datetime.datetime.strptime(row_data[ord("Q")-65],r'%Y/%m/%d %H:%M')
            now=datetime.datetime.now()
            # 如果2天都没物流信息发送错误
            if (now-table_time>datetime.timedelta(days=2)):
                item_error=error_data.get(name,[])
                item_error.append({
                    'i':row,
                    'order':row_data[ord("M")-65],
                    'type':'超时发送'
                })
                error_data[name]=item_error
                # 同步发送仓库人员
                ck_item_error=error_data.get('李冬',[])
                ck_item_error.append(item_error[-1])
                error_data['李冬']=ck_item_error
        elif row_data[ord("N")-65]!='完成':
            item_error=error_data.get(name,[])
            item_error.append({
                'i':row,
                'order':row_data[ord("M")-65],
                'type':'完成失败'
            })
            error_data[name]=item_error
                
    # print(error_data)
    # return
    
    for people in error_data:
        if not isinstance(people,str):
            continue
        if people in ['黄思韵','许余江胜','陈学耀']:
            continue
        errors_items=error_data[people]
        # 获取id
        user_id=None
        for p_item in peoples:
            if p_item in ['黄思韵','许余江胜','陈学耀']:
                continue
            if p_item.count(people)>0:
                user_id=peoples[p_item]
        if not user_id:
            
            f_api.send_msg('hipapa-627',f'寄样：无法找这个人user_id：{people}')
        print(user_id,people)
        msg='寄样错误提示\n'
        for error in errors_items:
            if error['type']=='超时发送':
                msg+=f'第 {error["i"]} 行 {error["order"]}, 超时2天返回物流单号(这个不是填错错误!!有疑问联系仓库同事)\n'
            else:
                msg+=f'第 {error["i"]} 行 {error["order"]} ,提交失败请到表格查看\n'
        msg+='\nPS:物流超时联系仓库！错误信息内有stock_crtzd_rk_ex 就是地址异常 请手动到E3手工填单\n如果不懂联系工程师-马健平'
        ok=f_api.send_msg(user_id=user_id,msg=msg)
        if ok:
            for error in errors_items:
                f_api.sheet_work_write(
                'A5bostiJMhnI8WtiopmcXHCWnob',
                '8ce518',
                f'R{error["i"]}',
                [['是']]
        )
        

def test():
    # [  ['廖健莉','KA&推广部','线下KA','线下KA','xx','158xxx','广东省xx', '中通（线上）','6974560290932:1','',None,None,None,None,None,None,None]
    # ]
    # rsp = api.stock_crtzd_rk_add(
                # 'QTCK202404020381', datetime.date.today().strftime('%Y-%m-%d'), '抖音仓', '抖音部', '抖音', '')
    # print(rsp)
    exit()


if __name__ == "__main__":
    # rsp = get_djbh()
    # print(rsp)
    # test()
    main()
    send_error()
